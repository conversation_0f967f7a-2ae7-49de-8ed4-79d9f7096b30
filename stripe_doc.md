# Stripe Subscription Webhook Events - Core Flows

Stripe's `customer.subscription.*` events are fired in a specific order depending on the subscription lifecycle. Here are the **complete event orders** for the core subscription flows:

## 🔄 **1. Subscription Creation**

When you create a subscription:

1. `customer.subscription.created` – Immediately when the subscription object is created (status might be `incomplete` if authentication required)
2. `payment_intent.created` – PaymentIntent is created for the payment
3. `invoice.created` – A new invoice is generated for the first billing cycle
4. `invoice.finalized` – When the invoice is ready to be paid (may be delayed up to 72 hours if webhook response isn't received)
5. `invoice.payment_succeeded` or `invoice.payment_failed` – Payment result
6. `payment_intent.succeeded` (if payment succeeds) – PaymentIntent completion
7. `invoice.paid` – Invoice status becomes `paid` (if payment succeeds)
8. `customer.subscription.updated` – Reflects changes after invoice payment (e.g., `status` becomes `active`)

**Alternative flows:**
- If payment requires authentication: `invoice.payment_action_required`
- If invoice finalization fails: `invoice.finalization_failed`

## 📅 **2. Subscription Renewal**

When a subscription is about to renew:

1. `invoice.upcoming` – Configurable days before renewal (default 7 days, can be changed in Dashboard)
2. `payment_intent.created` – PaymentIntent created for renewal payment
3. `invoice.created` – On the renewal date
4. `invoice.finalized` – Invoice ready for payment
5. `invoice.payment_succeeded` or `invoice.payment_failed` – Payment result
6. `payment_intent.succeeded` (if successful) – PaymentIntent completion
7. `invoice.paid` (if successful) – Invoice marked as paid
8. `customer.subscription.updated` – Status changes (remains `active`, or becomes `past_due` if payment fails)

## 📉 **3. Payment Failures**

When a payment fails:

1. `invoice.payment_failed` – Payment attempt failed
2. `payment_intent.payment_failed` – PaymentIntent status changes
3. `customer.subscription.updated` – `status` becomes `past_due`
4. *(Smart Retries may trigger additional payment attempts)*
5. If all retries fail:
   - `customer.subscription.updated` – Status may become `unpaid` or `canceled` (based on your subscription settings)

**Retry sequence (Smart Retries enabled):**
```
invoice.payment_failed
payment_intent.payment_failed
customer.subscription.updated (past_due)
[Retry attempt]
payment_intent.created (new attempt)
invoice.payment_succeeded OR invoice.payment_failed
payment_intent.succeeded OR payment_intent.payment_failed
customer.subscription.updated (active OR remains past_due)
```

## 🔄 **4. Plan Change (Upgrade/Downgrade)**

When you change the plan mid-cycle:

1. `customer.subscription.updated` – Reflects the plan change
2. `payment_intent.created` – For any immediate payment needed (prorations)
3. `invoice.created` – Prorated invoice may be created
4. `invoice.finalized` – Invoice ready for payment
5. `invoice.payment_succeeded` or `invoice.payment_failed` – Payment result for proration
6. `payment_intent.succeeded` (if successful) – PaymentIntent completion
7. `invoice.paid` (if successful) – Prorated invoice marked as paid
8. `customer.subscription.updated` – Final status update after payment

**Note:** Plan changes may not always create invoices if the proration amount is zero or if you're downgrading with credits.

## ⚠️ **Critical Timing Considerations**

1. **Invoice Finalization Delays**: With automatic collection enabled, Stripe waits up to 72 hours for successful webhook responses to `invoice.created` before finalizing invoices
2. **Payment Processing**: Stripe waits 1 hour after `invoice.finalized` before attempting payment
3. **Webhook Retries**: Failed webhook responses are retried for up to 3 days in live mode
4. **Event Order**: While this represents typical order, network conditions and processing delays may occasionally cause minor variations

## ✅ **Summary - Complete Flows**

**Successful creation:**
```
customer.subscription.created
payment_intent.created
invoice.created
invoice.finalized
invoice.payment_succeeded
payment_intent.succeeded
invoice.paid
customer.subscription.updated
```

**Successful renewal:**
```
invoice.upcoming
payment_intent.created
invoice.created
invoice.finalized
invoice.payment_succeeded
payment_intent.succeeded
invoice.paid
customer.subscription.updated
```

**Payment failure:**
```
invoice.payment_failed
payment_intent.payment_failed
customer.subscription.updated (past_due)
```

**Plan change with proration:**
```
customer.subscription.updated
payment_intent.created
invoice.created
invoice.finalized
invoice.payment_succeeded
payment_intent.succeeded
invoice.paid
customer.subscription.updated
```

## 📋 **Implementation Tips**

1. **Handle both invoice and payment_intent events**: Don't rely solely on one type
2. **Monitor subscription status changes**: Key statuses are `active`, `past_due`, `canceled`, `unpaid`, `incomplete`
3. **Handle finalization failures**: Listen for `invoice.finalization_failed`
4. **Account for timing delays**: The 72-hour delay in invoice finalization can affect your flow
5. **Implement idempotency**: Events may be delivered multiple times