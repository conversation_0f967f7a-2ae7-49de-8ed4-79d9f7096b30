{"imports": {"/": "./hub/", "./": "./", "@": "./hub/@reframe/zero/create/runtime.ts", "@libsql/kysely-libsql": "npm:@libsql/kysely-libsql@^0.4.1", "@reframe/": "./hub/@reframe/", "@retune/": "./hub/@retune/", "@script-company/": "./hub/@script-company/", "@dj/": "./hub/@dj/", "@w/": "./hub/@w/", "npm:react@canary": "https://esm.sh/react@canary", "npm:react-dom@canary": "https://esm.sh/react-dom@canary", "npm:react-reconciler@canary": "https://esm.sh/react-reconciler@canary", "npm:react-reconciler": "https://esm.sh/react-reconciler", "react/jsx-runtime": "https://esm.sh/react@canary/jsx-runtime"}, "compilerOptions": {"jsx": "react-jsx", "jsxImportSource": "https://esm.sh/react@canary", "jsxImportSourceTypes": "npm:@types/react@^18.3", "lib": ["esnext", "dom", "dom.iterable", "deno.ns", "deno.unstable"]}, "unstable": ["cron", "kv", "broadcast-channel"], "react": "19.0.0-rc-58af67a8f8-20240628", "tasks": {"watch": "cd hub; deno run --allow-all --watch @reframe/zero/run.ts --watch=true", "run": "cd hub; deno run --allow-all @reframe/zero/run.ts", "build": "cd hub; deno run --allow-all @reframe/zero/build/main.ts", "deploy": "cd hub; deno run --allow-all @reframe/zero/deploy.ts", "create:app": "cd hub; deno run --allow-all @reframe/cli/create/app.ts", "db:migrate": "cd hub; deno run --allow-all @reframe/db/migrate.ts", "clean": "rm -r .cache/*", "flyreset": "deno run --allow-net fly-reset.ts", "studio:prod": "deno run -A npm:drizzle-kit studio --config=drizzle.config.ts --port=4310", "studio:test": "deno run -A npm:drizzle-kit studio --config=drizzle-test.config.ts --port=4311", "studio:local": "deno run -A npm:drizzle-kit studio --config=drizzle-local.config.ts --port=4312"}}