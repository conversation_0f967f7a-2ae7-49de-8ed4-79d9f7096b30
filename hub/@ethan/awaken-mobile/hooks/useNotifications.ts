import { useEffect, useRef } from 'react';
import * as Notifications from 'expo-notifications';
import NotificationService from '../services/NotificationService';

export interface UseNotificationsOptions {
  channelId?: string;
  onNotificationReceived?: (notification: Notifications.Notification) => void;
  onNotificationResponse?: (response: Notifications.NotificationResponse) => void;
}

export function useNotifications(options: UseNotificationsOptions = {}) {
  const { channelId, onNotificationReceived, onNotificationResponse } = options;
  const notificationListener = useRef<Notifications.Subscription>();
  const responseListener = useRef<Notifications.Subscription>();

  useEffect(() => {
    // Initialize notification service when channelId is available
    if (channelId) {
      NotificationService.initialize(channelId);
    }

    // Set up notification listeners
    notificationListener.current = Notifications.addNotificationReceivedListener(notification => {
      console.log('Notification received:', notification);
      onNotificationReceived?.(notification);
    });

    responseListener.current = Notifications.addNotificationResponseReceivedListener(response => {
      console.log('Notification response:', response);
      onNotificationResponse?.(response);
    });

    return () => {
      if (notificationListener.current) {
        Notifications.removeNotificationSubscription(notificationListener.current);
      }
      if (responseListener.current) {
        Notifications.removeNotificationSubscription(responseListener.current);
      }
    };
  }, [channelId, onNotificationReceived, onNotificationResponse]);

  const requestPermissions = async () => {
    return await NotificationService.requestPermissionsAndGetToken();
  };

  const getCurrentToken = () => {
    return NotificationService.getCurrentToken();
  };

  return {
    requestPermissions,
    getCurrentToken,
  };
}
