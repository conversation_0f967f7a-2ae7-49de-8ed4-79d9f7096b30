/**
 * This file was generated by kysely-codegen.
 * Please do not edit it manually.
 */

import type { ColumnType } from "npm:kysely";

export type Generated<T> = T extends ColumnType<infer S, infer I, infer U>
  ? ColumnType<S, I | undefined, U>
  : ColumnType<T, T | undefined, T>;

export interface _CoachCardOfferingsOld {
  cardDefinitionId: string;
  coachName: string;
  createdAt: Generated<string>;
  data: Generated<string>;
  descriptionOverride: string | null;
  id: string | null;
  isVisible: Generated<number>;
  requiresBetaAccess: Generated<number>;
  sortOrder: Generated<number>;
  titleOverride: string | null;
  type: string;
  uiConfigOverride: string | null;
  updatedAt: Generated<string>;
}

export interface CallDetails {
  callDuration: number | null;
  callId: string | null;
  channelId: string;
  controlUrl: string;
  cost: number | null;
  createdAt: Generated<string>;
  endedAt: string | null;
  essenceCost: number | null;
  startedAt: string | null;
  status: string | null;
  summary: string | null;
}

export interface CardDefinitions {
  createdAt: Generated<string>;
  defaultDescription: string | null;
  defaultTitle: string;
  defaultUiConfig: Generated<string>;
  id: string | null;
  isSystemStandard: Generated<number>;
  requiresUserInstance: Generated<number>;
  type: string;
  updatedAt: Generated<string>;
}

export interface CoachCardOfferings {
  cardDefinitionId: string;
  coachName: string;
  createdAt: Generated<string>;
  data: Generated<string>;
  descriptionOverride: string | null;
  id: string | null;
  isVisible: Generated<number>;
  requiresBetaAccess: Generated<number>;
  sortOrder: Generated<number>;
  titleOverride: string | null;
  type: string;
  uiConfigOverride: string | null;
  updatedAt: Generated<string>;
}

export interface Coaches {
  costMultiplier: Generated<number>;
  createdAt: Generated<string>;
  defaultCoach: Generated<number | null>;
  description: string | null;
  email: string | null;
  id: string;
  metadata: Generated<string>;
  name: string;
  prompts: Generated<string>;
  type: string | null;
}

export interface Conversation {
  airtableId: string | null;
  audio: string | null;
  channelId: number;
  coachName: Generated<string>;
  content: string;
  createdAt: Generated<string>;
  date: string;
  id: string | null;
  messageType: Generated<string | null>;
  seenByCoach: Generated<number | null>;
  seenByUser: Generated<number | null>;
  sender: string;
  status: Generated<string | null>;
}

export interface DailyDigestJob {
  coachCount: Generated<number>;
  createdAt: string;
  digestDate: string;
  id: string | null;
  messageCount: Generated<number>;
  sentAt: string | null;
  userId: string;
}

export interface EmailJob {
  cancelled: Generated<number>;
  coachId: string;
  createdAt: string;
  id: string | null;
  sendAt: string;
  sentAt: string | null;
  stage: number;
  type: string;
  userId: string;
}

export interface GiftCodes {
  appliedAt: string | null;
  appliedChannelId: number | null;
  code: string;
  createdAt: Generated<string | null>;
  essenceAmount: number;
  id: Generated<number | null>;
  message: string | null;
  purchaserEmail: string;
  recipientEmail: string | null;
  senderName: string | null;
  stripePaymentIntentId: string;
}

export interface KnowledgeBases {
  content: string;
  createdAt: Generated<string>;
  id: Generated<number | null>;
  identifier: string;
  title: string | null;
  updatedAt: Generated<string>;
}

export interface Notice {
  createdAt: string;
  createdBy: string;
  id: string;
  message: string;
}

export interface ProfileText {
  channelId: number | null;
  content: string | null;
  createdAt: Generated<string | null>;
}

export interface ProfileTextDump {
  channelId: number | null;
  content: string | null;
  createdAt: Generated<string | null>;
}

export interface Prompts {
  createdAt: Generated<string>;
  description: string | null;
  name: string;
  promptJSON: string;
  promptText: string;
}

export interface StripeEvents {
  eventId: string;
  processedAt: Generated<string>;
}

export interface Subscription {
  cancelAtPeriodEnd: number | null;
  canceledAt: string | null;
  coachAccess: Generated<string | null>;
  createdAt: string;
  currentPeriodEnd: string;
  currentPeriodStart: string;
  id: string;
  planId: string;
  status: string;
  userEmail: string;
  userId: string | null;
}

export interface Summary {
  channelId: number;
  id: string;
  summarySent: number;
}

export interface User {
  addedEssenceBalance: Generated<number>;
  agentId: string | null;
  agentName: string | null;
  callCountToday: Generated<number | null>;
  callMinsThisPeriod: Generated<number | null>;
  callMinutesToday: Generated<string | null>;
  channelId: number;
  coachAccess: Generated<string | null>;
  coachId: string | null;
  coachMode: string | null;
  coachName: string | null;
  createdAt: string;
  currentPlan: Generated<string | null>;
  email: string;
  emailDaily: Generated<number | null>;
  feedbackLife: Generated<number | null>;
  firstMessage: number | null;
  image: string | null;
  lastDailyAwakeningDate: string | null;
  lastDailyReset: string | null;
  lastMonthlyReset: string | null;
  lastNoticeSeenAt: string | null;
  limitlessApiKey: string | null;
  limitlessCoach: string | null;
  limitlessEnabled: Generated<number>;
  messageCountToday: Generated<number | null>;
  messagesThisPeriod: Generated<number | null>;
  messagesTillFeedback: Generated<number | null>;
  messagesTillProfileUpdate: Generated<number>;
  metaFeedback: string | null;
  monthlyEssenceBalance: Generated<number>;
  name: string | null;
  onboarding: number | null;
  planStartDate: string | null;
  profileInitiated: Generated<number>;
  profileText: string | null;
  selectedCoach: Generated<string | null>;
  selectedVoices: Generated<string | null>;
  status: Generated<string>;
  stripeCustomerId: string | null;
  stripeSubscriptionId: string | null;
  timezone: Generated<string | null>;
  totalCallMins: Generated<number | null>;
  totalMessages: Generated<number | null>;
  verifiedToken: string | null;
}

export interface UserCards {
  coachOfferingId: string;
  createdAt: Generated<string>;
  data: Generated<string>;
  descriptionOverride: string | null;
  id: string | null;
  isVisible: Generated<number>;
  lastUsedAt: string | null;
  sortOrder: Generated<number>;
  status: Generated<string | null>;
  titleOverride: string | null;
  type: string;
  updatedAt: Generated<string>;
  userChannelId: number;
}

export interface UserCoachAttributes {
  channelId: number;
  coachName: string;
  coachNotes: Generated<string>;
  dataSharingEnabled: Generated<number>;
  messagesTillFeedback: Generated<number | null>;
  metaFeedback: Generated<string | null>;
}

export interface UserCoachTags {
  channelId: number;
  coachName: string;
  createdAt: Generated<string>;
  tag: string;
}

export interface DB {
  _CoachCardOfferingsOld: _CoachCardOfferingsOld;
  callDetails: CallDetails;
  cardDefinitions: CardDefinitions;
  coachCardOfferings: CoachCardOfferings;
  coaches: Coaches;
  conversation: Conversation;
  dailyDigestJob: DailyDigestJob;
  emailJob: EmailJob;
  giftCodes: GiftCodes;
  knowledgeBases: KnowledgeBases;
  notice: Notice;
  profileText: ProfileText;
  profileTextDump: ProfileTextDump;
  prompts: Prompts;
  stripeEvents: StripeEvents;
  subscription: Subscription;
  summary: Summary;
  user: User;
  userCards: UserCards;
  userCoachAttributes: UserCoachAttributes;
  userCoachTags: UserCoachTags;
}
