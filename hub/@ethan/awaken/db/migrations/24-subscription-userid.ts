import { Kysely } from "npm:kysely";

export async function up(db: <PERSON>ysely<unknown>): Promise<void> {
  // Add subscription-related columns to user table
 //   does not need to be not null
  await db.schema
    .alterTable("subscription")
    .addColumn("user_id", "integer")
    .execute();
}

export async function down(db: Kysely<any>): Promise<void> {
  await db.schema.dropTable("subscription").execute();
  
  // Remove subscription columns from user table
  const columns = [
    "user_id"
  ];

  for (const column of columns) {
    await db.schema.alterTable("subscription").dropColumn(column).execute();
  }
} 