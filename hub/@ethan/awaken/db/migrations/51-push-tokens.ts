import { Kysely, sql } from "npm:kysely";

export async function up(db: Kysely<any>): Promise<void> {
  await db.schema
    .createTable("push_tokens")
    .addColumn("id", "text", (col) => col.primary<PERSON>ey())
    .addColumn("channel_id", "integer", (col) => col.notNull())
    .addColumn("token", "text", (col) => col.notNull())
    .addColumn("token_type", "text", (col) => col.notNull().defaultTo("expo")) // expo, apns, fcm
    .addColumn("platform", "text", (col) => col.notNull()) // ios, android
    .addColumn("is_active", "boolean", (col) => col.notNull().defaultTo(true))
    .addColumn("created_at", "text", (col) => 
      col.defaultTo(sql`CURRENT_TIMESTAMP`).notNull()
    )
    .addColumn("updated_at", "text", (col) => 
      col.defaultTo(sql`CURRENT_TIMESTAMP`).notNull()
    )
    .execute();

  // Create indexes for efficient queries
  await db.schema
    .createIndex("push_tokens_channel_idx")
    .on("push_tokens")
    .column("channel_id")
    .execute();
    
  await db.schema
    .createIndex("push_tokens_token_idx")
    .on("push_tokens")
    .column("token")
    .execute();

  await db.schema
    .createIndex("push_tokens_active_idx")
    .on("push_tokens")
    .column("is_active")
    .execute();

  // Add composite index on channelId and active status for faster queries
  await db.schema
    .createIndex("push_tokens_channel_active_idx")
    .on("push_tokens")
    .columns(["channel_id", "is_active"])
    .execute();

  console.log("Created push_tokens table and indexes");
}

export async function down(db: Kysely<any>): Promise<void> {
  await db.schema.dropIndex("push_tokens_channel_idx").execute();
  await db.schema.dropIndex("push_tokens_token_idx").execute();
  await db.schema.dropIndex("push_tokens_active_idx").execute();
  await db.schema.dropIndex("push_tokens_channel_active_idx").execute();
  await db.schema.dropTable("push_tokens").execute();
  
  console.log("Dropped push_tokens table and indexes");
}
