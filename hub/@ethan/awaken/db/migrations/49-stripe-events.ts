import { Kysely, sql } from "npm:kysely";

export async function up(db: <PERSON>ysely<unknown>): Promise<void> {
  await db.schema
    .createTable("stripe_events")
    .addColumn("event_id", "text", col => col.primaryKey().notNull())
    .addColumn("processed_at", "timestamp", col => col.notNull().defaultTo(sql`CURRENT_TIMESTAMP`))
    .execute();
}

export async function down(db: Kysely<unknown>): Promise<void> {
  await db.schema.dropTable("stripe_events").execute();
}
