import { Kysely, sql } from "npm:kysely";

export async function up(db: <PERSON>ysely<any>): Promise<void> {
  // 1. Add limitlessEnabled column
  try {
    await db.schema
      .alterTable("user")
      .addColumn("limitless_enabled", "boolean", (col) => col.notNull().defaultTo(false))
      .execute();
    console.log("Added limitless_enabled column to user table");
  } catch (e) {
    if (e.message.includes("duplicate column")) {
      console.log("limitless_enabled column already exists, skipping");
    } else {
      throw e;
    }
  }

  // 2. Add limitlessApiKey column
  try {
    await db.schema
      .alterTable("user")
      .addColumn("limitless_api_key", "text", (col) => col)
      .execute();
    console.log("Added limitless_api_key column to user table");
  } catch (e) {
    if (e.message.includes("duplicate column")) {
      console.log("limitless_api_key column already exists, skipping");
    } else {
      throw e;
    }
  }

  // 3. Add limitlessCoach column
  try {
    await db.schema
      .alterTable("user")
      .addColumn("limitless_coach", "text", (col) => col)
      .execute();
    console.log("Added limitless_coach column to user table");
  } catch (e) {
    if (e.message.includes("duplicate column")) {
      console.log("limitless_coach column already exists, skipping");
    } else {
      throw e;
    }
  }

  console.log("Migration 48-limitless-integration completed");
}

export async function down(db: Kysely<any>): Promise<void> {
  // Drop columns in reverse order
  try {
    await db.schema
      .alterTable("user")
      .dropColumn("limitless_coach")
      .execute();
    console.log("Dropped limitless_coach column");
  } catch (e) {
    console.log("Failed to drop limitless_coach column:", e.message);
  }

  try {
    await db.schema
      .alterTable("user")
      .dropColumn("limitless_api_key")
      .execute();
    console.log("Dropped limitless_api_key column");
  } catch (e) {
    console.log("Failed to drop limitless_api_key column:", e.message);
  }

  try {
    await db.schema
      .alterTable("user")
      .dropColumn("limitless_enabled")
      .execute();
    console.log("Dropped limitless_enabled column");
  } catch (e) {
    console.log("Failed to drop limitless_enabled column:", e.message);
  }
}