// Migration to create gift_codes table for Gift Essence feature
import { Kysely, sql } from "npm:kysely";

export async function up(db: Kysely<any>): Promise<void> {
  await db.schema
    .createTable('gift_codes')
    .addColumn('id', 'integer', col => col.primaryKey().autoIncrement())
    .addColumn('code', 'varchar(8)', col => col.unique().notNull())
    .addColumn('essence_amount', 'integer', col => col.notNull())
    .addColumn('purchaser_email', 'varchar', col => col.notNull())
    .addColumn('recipient_email', 'varchar')
    .addColumn('sender_name', 'varchar')
    .addColumn('message', 'text')
    .addColumn('stripe_payment_intent_id', 'varchar', col => col.unique().notNull())
    .addColumn('applied_channel_id', 'integer')
    .addColumn('applied_at', 'timestamp')
    .addColumn('created_at', 'timestamp', col => col.defaultTo(sql`CURRENT_TIMESTAMP`))
    .execute();

  // Add index for faster code lookups
  await db.schema
    .createIndex('gift_codes_code_idx')
    .on('gift_codes')
    .column('code')
    .execute();

  // Add index for finding gifts by recipient email
  await db.schema
    .createIndex('gift_codes_recipient_email_idx')
    .on('gift_codes')
    .column('recipient_email')
    .execute();
}

export async function down(db: Kysely<any>): Promise<void> {
  await db.schema.dropTable('gift_codes').execute();
}