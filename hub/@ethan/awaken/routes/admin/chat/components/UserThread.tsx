"use client";

import { useEffect, useMemo, useRef, useState } from "npm:react@canary";
import { But<PERSON>, ScrollArea, Text } from "@reframe/ui/main.tsx";
import { CircleXIcon } from "@reframe/icons/circle-x.ts";
import { MicIcon } from "@reframe/icons/mic.ts";
import { MessageItem } from "./MessageItem.tsx";
import { markThreadMessagesSeenByCoach, sendCoachMessage, sendCoachVoiceMessage } from "../../actions/admin-actions.ts";
import { createPortal } from "npm:react-dom@canary";
import { animated, useTransition } from "npm:@react-spring/web@9.7.3";
import { VoiceInput } from "../../../../routes/chat/chat-message/components/VoiceInput.tsx";

interface ThreadProps {
  user: User;
  visible: boolean;
  onClose: () => void;
  allMessages: any[]; // All messages from parent - will filter for coach_message internally
  initialMessageId: string | null;
  coachName: string;
  channelId: number | string;
  setMessages: (messages: any[]) => void;
}

/**
 * An overlay that displays all `coach_message` items between a coach and a user.
 * Copied from the user-side `CoachThread` component and adapted for the admin view.
 */
export const UserThread: React.FC<ThreadProps> = ({
  user,
  visible,
  onClose,
  allMessages,
  initialMessageId,
  coachName,
  channelId,
  setMessages,
}) => {
  // Do not attempt to render on the server
  if (typeof document === "undefined") return null;

  // Filter thread messages from all messages using useMemo for proper dependency tracking
  const threadMsgs = useMemo(() => {
    const filtered = allMessages.filter(m => m.messageType === 'coach_message');
    console.log("[UserThread] Filtering messages:", {
      totalMessages: allMessages.length,
      filteredThreadMessages: filtered.length,
      lastFewFiltered: filtered.slice(-3).map(m => ({ id: m.id, content: m.content?.substring(0, 50), date: m.date })),
      lastFewAll: allMessages.slice(-3).map(m => ({ id: m.id, type: m.messageType, content: m.content?.substring(0, 50), date: m.date }))
    });
    return filtered;
  }, [allMessages]);

  // Debug: Track when allMessages prop changes and log what messages are being processed
  useEffect(() => {
    console.log("[UserThread] allMessages prop updated:", {
      messageCount: allMessages.length,
      visible: visible,
      coachName: coachName,
      channelId: channelId,
      threadMessageCount: threadMsgs.length,
      lastThreadMessage: threadMsgs.length > 0 ? {
        id: threadMsgs[threadMsgs.length - 1]?.id,
        content: threadMsgs[threadMsgs.length - 1]?.content?.substring(0, 50),
        sender: threadMsgs[threadMsgs.length - 1]?.sender,
        date: threadMsgs[threadMsgs.length - 1]?.date
      } : null
    });
  }, [allMessages, visible, coachName, channelId, threadMsgs]);

  /**
   * Local state for tracking which message should be highlighted. We start with
   * `initialMessageId` (if any) received from the parent component. After
   * 3 seconds the highlight is cleared so the UI returns to normal.
   */
  const [highlightId, setHighlightId] = useState<string | null>(null);

  // Ref to store the timeout so it can be cleared on unmount or visibility toggle
  const highlightTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Synchronise highlight state with visibility and selected anchor
  useEffect(() => {
    // Whenever visibility changes, reset highlight state accordingly
    if (visible) {
      setHighlightId(initialMessageId);
    } else {
      setHighlightId(null);
    }
  }, [visible, initialMessageId]);

  // Manage highlight timeout – clear any existing timer first, then start a new one
  useEffect(() => {
    if (highlightTimerRef.current) {
      clearTimeout(highlightTimerRef.current);
      highlightTimerRef.current = null;
    }

    if (highlightId) {
      highlightTimerRef.current = setTimeout(() => {
        setHighlightId(null);
        highlightTimerRef.current = null;
      }, 3000);
    }

    // Clean-up on unmount
    return () => {
      if (highlightTimerRef.current) {
        clearTimeout(highlightTimerRef.current);
        highlightTimerRef.current = null;
      }
    };
  }, [highlightId]);

  // Mark unseen thread messages as seen by the coach once the overlay is visible
  useEffect(() => {
    if (!visible) return;

    const hasUnseen = threadMsgs.some(
      (m) => m.sender === "user" && !m.seenByCoach
    );
    if (!hasUnseen) return;

    // Update on the server
    markThreadMessagesSeenByCoach(channelId, coachName)
      .catch((err) => console.error("Failed to mark thread messages seen", err));

    // Update the parent's messages state so the UI reflects the change immediately
    setMessages((prev) =>
      prev.map((m) =>
        m.messageType === 'coach_message' && m.sender === "user" && !m.seenByCoach
          ? { ...m, seenByCoach: true }
          : m
      )
    );
  }, [visible, threadMsgs, channelId, coachName, setMessages]);

  const [input, setInput] = useState("");
  const scrollRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const [isRecordingMode, setIsRecordingMode] = useState(false);
  const [activeAudioId, setActiveAudioId] = useState<number | null>(null);

  // Scroll the highlighted message (if any) into view whenever it changes
  useEffect(() => {
    if (!scrollRef.current || !highlightId) return;
    const timer = setTimeout(() => {
      const el = scrollRef.current!.querySelector(
        `[data-thread-id="${highlightId}"]`
      ) as HTMLElement | null;
      if (el) {
        el.scrollIntoView({ block: "center" });
      }
    }, 100);
    return () => clearTimeout(timer);
  }, [highlightId, threadMsgs]);

  // Automatically scroll to the bottom when new thread messages arrive
  useEffect(() => {
    if (!visible || !scrollRef.current) return;
    
    // Scroll to bottom when new messages are added
    requestAnimationFrame(() => {
      if (scrollRef.current) {
        scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
      }
    });
  }, [threadMsgs.length, visible]); // Depend on message count changes

  // Automatically scroll to the bottom when the overlay becomes visible and no
  // specific anchor was provided (e.g. opened via the header button).
  useEffect(() => {
    if (!visible || !scrollRef.current) return;
    if (initialMessageId) return; // anchor scroll will handle positioning

    // Allow the DOM to finish rendering then jump to bottom
    requestAnimationFrame(() => {
      if (scrollRef.current) {
        scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
      }
    });
  }, [visible, initialMessageId]);

  // Auto-resize textarea
  useEffect(() => {
    const textarea = textareaRef.current;
    if (!textarea) return;
    textarea.style.height = "auto";
    textarea.style.height = `${Math.min(textarea.scrollHeight, 150)}px`;
  }, [input]);

  // Handle Enter vs Shift+Enter behaviour
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);
    if (e.key === "Enter") {
      if ((isMobile && e.shiftKey) || (!isMobile && !e.shiftKey)) {
        e.preventDefault();
        if (input.trim()) {
          handleSend();
        }
      }
    }
  };

  // Send a new coach message
  const handleSend = async () => {
    const content = input.trim();
    if (!content) return;
    try {
      const saved = await sendCoachMessage(channelId, { name: coachName, email: "" }, content);
      if (saved) {
        // Only update the parent's messages state - threadMsgs will automatically update
        setMessages(prev => [...prev, saved]);
        setInput("");
        requestAnimationFrame(() => {
          scrollRef.current?.scrollTo({ top: scrollRef.current.scrollHeight });
        });
      }
    } catch (err) {
      console.error("sendCoachMessage error", err);
    }
  };

  // Handle voice recording completion
  const handleVoiceComplete = async (audioBlob: Blob) => {
    // Optimistic insert – same shape as MessageItem expects
    const tempId = crypto.randomUUID();        // globalThis.crypto is available in browser
    const audioUrl = URL.createObjectURL(audioBlob);
    const optimistic: any = {
      id: tempId,
      channelId: Number(channelId),
      sender: "coach",
      content: "Transcribing...",  // Show transcribing status
      audio: audioUrl,
      date: new Date().toISOString(),
      status: "Default",
      messageType: "coach_message",
      coachName: coachName,
      isTemporary: true,
      // These uppercase properties are what MessageItem expects
      Id: tempId,
      Sender: "coach",
      Content: "Transcribing...",
      Audio: audioUrl,
      Date: new Date().toISOString(),
      Status: "Default",
      Type: "coach_message",
      CoachName: coachName,
    };
    // Only update the parent's messages state - threadMsgs will automatically update
    setMessages((prev) => [...prev, optimistic]);

    try {
      const saved = await sendCoachVoiceMessage(channelId, { name: coachName }, audioBlob);
      
      // Check if save was successful
      if (!saved || !saved.id) {
        throw new Error("Failed to save voice message");
      }
      
      // Create properly formatted message object matching what sendCoachMessage returns
      const formattedMessage = {
        id: saved.id,
        channelId: Number(channelId),
        sender: "coach",
        content: saved.fields.Content || "[Voice message]", // Show transcript or fallback
        date: saved.fields.Date,
        status: saved.fields.Status,
        messageType: saved.fields.Type,
        audio: saved.fields.Audio,
        coachName: saved.fields.CoachName,
        // These uppercase properties are what MessageItem expects
        Id: saved.id,
        Sender: "coach",
        Content: saved.fields.Content || "[Voice message]", // Show transcript or fallback
        Audio: saved.fields.Audio,
        Date: saved.fields.Date,
        Status: saved.fields.Status,
        Type: saved.fields.Type,
        CoachName: saved.fields.CoachName,
      };
      
      // Replace optimistic with real saved row - only update parent state
      setMessages((prev) =>
        prev.map((m) => (m.Id === tempId || m.id === tempId ? formattedMessage : m))
      );
      
      // Clean up the temporary blob URL
      URL.revokeObjectURL(audioUrl);
      
      // Scroll to bottom after sending
      requestAnimationFrame(() => {
        scrollRef.current?.scrollTo({ top: scrollRef.current.scrollHeight });
      });
    } catch (err) {
      console.error("Voice message failed:", err);
      // Remove optimistic message on failure - only update parent state
      setMessages((prev) => prev.filter((m) => m.Id !== tempId && m.id !== tempId));
      // Clean up the temporary blob URL
      URL.revokeObjectURL(audioUrl);
      alert("Failed to send voice message.");   // or toast
    }
  };

  // Slide-in/out animation optimized for smoothness
  const slideTransition = useTransition(visible, {
    from: {
      transform: "translateY(100%)",
      opacity: 0,
    },
    enter: {
      transform: "translateY(0%)",
      opacity: 1,
      delay: 0, // Remove delay to prevent stuttering
    },
    leave: {
      transform: "translateY(100%)",
      opacity: 0, // Fade out the entire overlay quickly
      delay: 0, // No delay for exit
    },
    config: (key) => {
      // Use different configs for different properties
      if (key === 'opacity') {
        return {
          duration: visible ? 200 : 150, // Slightly longer fade in, fast fade out
          easing: (t) => t * t, // Ease-in for smoother start
        };
      }
      return {
        mass: 0.8,    // Reduced mass for snappier response
        tension: 400, // Higher tension for more responsive animation
        friction: 30, // Reduced friction for smoother motion
        clamp: true,  // Prevent overshoot/bounce
      };
    },
  });

  const overlay = slideTransition((style, item) =>
    item ? (
      <animated.div
        className="fixed inset-0 z-50 flex items-end justify-center bg-black/70 sm:pt-4 sm:px-4"
        style={{
          opacity: style.opacity,
        }}
        onClick={onClose}
      >
        <animated.div
          style={{
            transform: style.transform,
            willChange: "transform, opacity",
            backfaceVisibility: "hidden",
            perspective: 1000,
          }}
          onClick={(e) => e.stopPropagation()}
          className="w-full sm:max-w-3xl h-[85vh] bg-gradient-to-b from-[#0B0B0B] via-[#161616] to-[#0B0B0B] backdrop-blur-md border-t sm:border-l sm:border-r border-[#FCA31180] rounded-t-3xl flex flex-col"
        >
            {/* Header */}
            <div className="flex items-center justify-between pl-4 pr-1 py-4 border-b border-white/10 max-w-3xl mx-auto w-full">
              <Text className="text-white font-semibold">
                Thread with {user.userName}
              </Text>
              <Button
                variant="ghost"
                css="hover:bg-white/10 rounded-lg"
                onClick={onClose}
              >
                <CircleXIcon size={28} className="text-white" />
              </Button>
            </div>

            {/* Body */}
            <ScrollArea
              className="flex-1 overflow-y-auto p-4 space-y-4 w-full max-w-3xl mx-auto"
              ref={scrollRef}
            >
              {threadMsgs.map((m, idx) => {
                const isCoach = m.sender === "coach" || m.Sender === "coach";
                const noop = () => {};

                // Normalize message structure to handle both formats
                const normalizedMessage = {
                  Id: m.Id || m.id,
                  Sender: m.Sender || m.sender,
                  Content: m.Content || m.content,
                  Date: m.Date || m.date,
                  Type: m.Type || m.messageType || "coach_message",
                  CoachName: m.CoachName || m.coachName,
                  Status: m.Status || m.status,
                  Audio: m.Audio || m.audio,
                };

                return (
                  <div
                    key={normalizedMessage.Id}
                    data-thread-id={normalizedMessage.Id}
                    className={`flex flex-col w-full ${
                      isCoach ? "items-end" : "items-start"
                    } mb-3`}
                  >
                    <div className="w-full max-w-[90%]">
                      <MessageItem
                        message={normalizedMessage}
                        index={idx}
                        allMessages={threadMsgs.map((msg) => ({
                          Id: msg.Id || msg.id,
                          Sender: msg.Sender || msg.sender,
                          Content: msg.Content || msg.content,
                          Date: msg.Date || msg.date,
                          Type:
                            msg.Type || msg.messageType || "coach_message",
                          CoachName: msg.CoachName || msg.coachName,
                          Status: msg.Status || msg.status,
                          Audio: msg.Audio || msg.audio,
                        }))}
                        contentReady
                        currentLetterIndex={0}
                        activeAudioId={activeAudioId}
                        setActiveAudioId={setActiveAudioId}
                        toggleStar={noop}
                        hideStarToggle
                        showCoachPill={false}
                        highlight={normalizedMessage.Id === highlightId}
                        showHumanCoachName={false}
                      />
                    </div>
                  </div>
                );
              })}
            </ScrollArea>

            {/* Input */}
            <div className="border-t border-white/10 p-4 max-w-3xl mx-auto w-full">
              {isRecordingMode
                ? (
                  <VoiceInput
                    channelId={String(channelId)}
                    isRecordingMode={isRecordingMode}
                    setIsRecordingMode={setIsRecordingMode}
                    onRecordingComplete={handleVoiceComplete}
                  />
                )
                : (
                  <div className="flex gap-3">
                    <textarea
                      ref={textareaRef}
                      className="flex-1 bg-white/10 text-white placeholder-gray-400 border border-white/20 rounded-lg px-4 py-2 focus:outline-none focus:border-orange-500 focus:ring-1 focus:ring-orange-500 resize-none overflow-y-auto"
                      placeholder="Reply..."
                      rows={1}
                      value={input}
                      onChange={(e) => setInput(e.target.value)}
                      onKeyDown={handleKeyDown}
                      style={{ minHeight: "48px", maxHeight: "150px" }}
                    />
                    <Button
                      variant="ghost"
                      onClick={() => setIsRecordingMode(true)}
                      css="px-3 py-2 hover:bg-white/10 rounded-lg"
                    >
                      <MicIcon className="w-5 h-5 text-white" />
                    </Button>
                    <Button
                      onClick={handleSend}
                      disabled={input.trim() === ""}
                      css={`${
                        input.trim() === ""
                          ? "bg-white/10 text-gray-400 cursor-not-allowed"
                          : "bg-orange-600 hover:bg-orange-700 text-white"
                      } px-4 py-2 rounded-lg`}
                    >
                      Send
                    </Button>
                  </div>
                )}
            </div>
        </animated.div>
      </animated.div>
    ) : null
  );

  return createPortal(overlay, document.body);
};
