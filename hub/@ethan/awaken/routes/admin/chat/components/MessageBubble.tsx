"use client";

import React from "npm:react@canary";
import { Button } from "@reframe/ui/main.tsx";
import { format } from "npm:date-fns";
import { MESSAGE_TYPES, isCallMessage } from '../../../../lib/message-types.ts';

// Define proper message type instead of using 'any'
interface Message {
  Id?: string;
  isTemporary?: boolean;
  Status?: string;
  Sender?: string;
  Content?: string;
  Audio?: string;
  Date?: string;
  CoachName?: string;
}

interface MessageBubbleProps {
  isUser: boolean;
  children: React.ReactNode;
  contentReady: boolean;
  highlight?: boolean;
}

interface AwakeningBubbleProps {
  children: React.ReactNode;
  message: Message; 
  toggleStar: (id: string, status: string) => void;
  contentReady: boolean;
}

const MessageBubble = React.memo(({ isUser, children, contentReady, highlight = false }: MessageBubbleProps) => {
  const userStyles = "bg-[#5b3109]/60 border border-amber-500/30 text-amber-50";
  const otherStyles = "bg-[#3c1c07]/60 border border-amber-700/30 text-amber-100";
  const highlightStyles = highlight ? "ring-2 ring-[#FCA311]" : "";

  return (
    <div
      className={`rounded-2xl px-4 py-3 message-bubble ${isUser ? userStyles : otherStyles} ${highlightStyles}`}
      style={{
        willChange: 'transform, opacity',
        opacity: contentReady ? 1 : 0,
        transition: 'opacity 200ms ease-out, background-color 200ms ease-out, border-color 200ms ease-out, color 200ms ease-out',
        width: 'fit-content',
      }}
    >
      {children}
    </div>
  );
});

interface MessageMetaProps {
  sender: string;
  date: string | Date;
  coachName?: string;
}

const MessageMeta = ({ sender, date, coachName = "Kokoro" }: MessageMetaProps) => {
  const isUser = sender === "user";
  const timestampColor = isUser ? "text-amber-200/70" : "text-amber-400/70";

  return (
    <div className="flex items-center mb-1 px-1">
      <span className="text-[#fcb645] text-[10px]">
        {isUser ? "You" : "You"}
      </span>
      <span className={`${timestampColor} text-[10px] ml-2`}>
        {date
          ? format(new Date(date), "h:mm a")
          : "Invalid time"}
      </span>
    </div>
  );
};

export { MessageBubble, MessageMeta };
