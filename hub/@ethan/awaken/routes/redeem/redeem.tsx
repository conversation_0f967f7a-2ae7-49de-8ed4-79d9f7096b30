"use client";

// Gift redemption landing page
import React, { useEffect, useState } from "npm:react@canary";
import { Y, Text, Button } from "@reframe/ui/main.tsx";
import { motion } from "npm:framer-motion";
import { EssenceIcon } from "../../lib/icons.tsx";
import { CheckIcon } from "@reframe/icons/check.ts";
import { getGiftDetails } from "./redeem-actions.ts";
import { Logo } from "../../lib/logo.tsx";
import { ParticleField } from "../../lib/particles.tsx";

interface RedeemPageProps {
  user?: any;
}

export const RedeemPage: React.FC<RedeemPageProps> = ({ user }) => {
  // Local state
  const [isRedeeming, setIsRedeeming] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [giftDetails, setGiftDetails] = useState<{
    amount?: number;
    balance?: number;
    senderName?: string;
    message?: string;
  }>({});

  // Extract query parameters from the current URL (works without react-router)
  const [giftCode, setGiftCode] = useState<string | null>(null);

  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const code = params.get('g');
    setGiftCode(code);

    if (!user && code) {
      console.log(`[Client] Gift code found: ${code}. Fetching details...`);
      getGiftDetails(code).then(details => {
        console.log("[Client] Received gift details:", details);
        if (details) {
          console.log("[Client] Setting gift details in state.");
          setGiftDetails(d => ({...d, ...details}));
        } else {
          console.log("[Client] No valid details returned from server.");
        }
      });
    }
  }, []);

  useEffect(() => {
    if (user && giftCode && !isRedeeming && !success && !error) {
      handleAutoRedeem();
    }
  }, [user, giftCode]);

  const handleAutoRedeem = async () => {
    if (!giftCode) return;
    
    setIsRedeeming(true);
    setError(null);

    try {
      const response = await fetch('/api/redeem', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ code: giftCode }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(errorText || 'Failed to redeem gift');
      }

      const result = await response.json();
      console.log('[REDEEM] Gift redemption result:', result);
      setGiftDetails(result);
      setSuccess(true);
      
      // Store gift redemption info for welcome screen
      const giftData = {
        amount: result.amount,
        senderName: result.senderName,
        message: result.message,
        timestamp: Date.now()
      };
      console.log('[REDEEM] Storing gift data in sessionStorage:', giftData);
      sessionStorage.setItem('giftRedemption', JSON.stringify(giftData));
      
      // Small delay to ensure sessionStorage is set before redirect
      setTimeout(() => {
        console.log('[REDEEM] Redirecting to chat...');
        window.location.href = '/chat';
      }, 100);
    } catch (err) {
      console.error('Redeem error:', err);
      setError(err instanceof Error ? err.message : 'Failed to redeem gift');
    } finally {
      setIsRedeeming(false);
    }
  };

  // If not authenticated, show sign-in prompt
  if (!user) {
    return (
      <Y css="relative min-h-screen bg-black text-white flex flex-col items-center justify-center p-4 text-center overflow-hidden">
        {/* Background effects */}
        <ParticleField />
        <motion.div
          className="absolute inset-0 m-auto w-[467px] h-[467px] rounded-full pointer-events-none"
          style={{
            background: "linear-gradient(135deg, #FF5727 0%, #FFC360 100%)",
            filter: "blur(100px)",
          }}
          initial={{ opacity: 0.1 }}
          animate={{ opacity: 0.4 }}
          transition={{ duration: 0 }}
        />

        {/* Foreground content */}
        <div className="relative z-10 flex flex-col items-center">
          <div className="mb-12 flex items-center space-x-2">
            <Logo size={25} />
            <span className="text-xl font-normal">awaken</span>
          </div>
          <h1 className="mb-4 text-4xl font-semibold md:text-5xl">
            <span className="bg-gradient-to-r from-[#FF5727] to-[#FFC360] bg-clip-text text-transparent">
              Awaken freedom.
              <br />
              Transform fear.
            </span>
          </h1>
          <p className="mb-8 text-lg text-gray-400 max-w-md">
            Awaken AI walks with you 24/7, guiding you to love, presence, and
            inner power in the raw moments of daily life.
          </p>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="w-full max-w-md text-center"
          >
            <div className="mb-8 flex flex-col items-center">
              <EssenceIcon size={64} className="mx-auto mb-4" style={{ color: "#ff6b35" }} />
              {giftDetails.senderName ? (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6 }}
                >
                  <Text css="text-2xl font-bold text-white text-center">
                    {giftDetails.senderName} gifted you {giftDetails.amount} Essence!
                  </Text>
                </motion.div>
              ) : (
                <div className="h-8" />
              )}
            </div>

            <Button
              onClick={() => {
                // Redirect to sign-in, preserving the gift parameters
                window.location.href = `/auth/sign-in?redirect=${encodeURIComponent(
                  window.location.pathname + window.location.search
                )}`;
              }}
              css="w-full bg-[#FCA311] text-black font-semibold py-3 rounded-lg hover:bg-[#FCA311]/90 transition-colors"
            >
              Sign In to Awaken to Redeem Your Gift
            </Button>

            <div style={{ marginTop: "0.5rem" }} /> {/* Adds more space (mt-10) */}
            <Text css="text-sm text-white">
              Don't have an account? You'll be able to create one.
            </Text>
        </div>
      </Y>
    );
  }

  // Authenticated view
  return (
    <Y css="min-h-screen bg-black text-white flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        className="max-w-md w-full"
      >
        {isRedeeming && (
          <Y css="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-2 border-[#FCA311] border-t-transparent mx-auto mb-4" />
            <Text css="text-xl">Redeeming your gift...</Text>
          </Y>
        )}

        {error && !isRedeeming && (
          <Y css="text-center">
            <div className="text-red-500 mb-4">
              <Text css="text-6xl mb-4">⚠️</Text>
            </div>
            <Text css="text-xl font-semibold mb-2">Oops!</Text>
            <Text css="text-gray-400 mb-6">{error}</Text>
            <Button
              onClick={() => window.location.href = '/chat'}
              css="bg-gray-800 text-white px-6 py-2 rounded-lg hover:bg-gray-700 transition-colors"
            >
              Go to Chat
            </Button>
          </Y>
        )}

        {success && !isRedeeming && (
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ type: "spring", damping: 15 }}
            className="text-center"
          >
            <div className="mb-6">
              <div className="w-20 h-20 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <CheckIcon size={40} color="#22c55e" />
              </div>
              <Text css="text-3xl font-bold mb-2">Gift Redeemed!</Text>
              
              {giftDetails.senderName && (
                <Text css="text-lg text-gray-300 mb-2">
                  From {giftDetails.senderName}
                </Text>
              )}
              
              <Text css="text-2xl text-[#FCA311] font-bold mb-4">
                +{giftDetails.amount} Essence
              </Text>
              
              {giftDetails.message && (
                <div className="bg-gray-900 rounded-lg p-4 mb-6 text-left">
                  <Text css="text-sm text-gray-400 mb-1">Personal message:</Text>
                  <Text css="text-gray-200">{giftDetails.message}</Text>
                </div>
              )}
              
              <Text css="text-gray-400 mb-6">
                Your balance: {giftDetails.balance} Essence
              </Text>
            </div>

            <Button
              onClick={() => window.location.href = '/chat'}
              css="w-full bg-[#FCA311] text-black font-semibold py-3 rounded-lg hover:bg-[#FCA311]/90 transition-colors"
            >
              Start Your Journey
            </Button>
          </motion.div>
        )}
      </motion.div>
    </Y>
  );
};