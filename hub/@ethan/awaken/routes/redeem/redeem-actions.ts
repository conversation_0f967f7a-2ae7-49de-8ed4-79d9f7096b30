"use server";

import { getGiftByCode } from "../../actions/db/gift-actions.ts";

export const getGiftDetails = async (code: string) => {
  console.log(`[Action] getGiftDetails called with code: ${code}`);
  if (!code) {
    console.log("[Action] No code provided.");
    return null;
  }
  try {
    const gift = await getGiftByCode(code);
    console.log(`[Action] Found gift in DB:`, gift);
    if (!gift || gift.applied_at) {
      console.log(`[Action] Gift is invalid or already applied.`, { giftExists: !!gift, applied: gift?.applied_at });
      return null;
    }
    const result = {
      senderName: gift.senderName,
      amount: gift.essenceAmount,
    };
    console.log("[Action] Returning gift details:", result);
    return result;
  } catch (error) {
    console.error("[Action] Error fetching gift details:", error);
    return null;
  }
}; 