"use client";

import React, { useEffect, useState } from "npm:react@canary";
import { motion } from "npm:framer-motion";
import { Button, Text } from "@reframe/ui/main.tsx";
import { Logo } from "../../lib/logo.tsx";
import { getAllNotices, createNotice } from "../../action.ts";
import { SUPERUSER_EMAILS } from "../../lib/constants.ts";
import { Layout } from "../../lib/layout.tsx";
import { getUserData } from "../../lib/db.ts";

interface Notice {
  id: string;
  Message: string;
  Date: string;
  CreatedBy: string;
}

export const Changelog = ({ user }) => {
  const [notices, setNotices] = useState<Notice[]>([]);
  const [newMessage, setNewMessage] = useState("");
  const [isSuperuser, setIsSuperuser] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [userData, setUserData] = useState<Record<string, unknown> | null>(null);

  useEffect(() => {
    console.log('[Changelog] Component mounted, user:', user?.email);
    
    const fetchNotices = async () => {
      try {
        console.log('[Changelog] Fetching notices');
        const allNotices = await getAllNotices();
        console.log('[Changelog] Notices fetched:', allNotices?.length);
        
        if (!Array.isArray(allNotices)) {
          console.error('[Changelog] Invalid notices data:', allNotices);
          setError('Failed to load notices');
          return;
        }
        
        setNotices(allNotices);
      } catch (err) {
        console.error('[Changelog] Error fetching notices:', err);
        setError('Failed to load notices');
      }
    };
    
    // Fetch user data for the Layout component
    const fetchUserData = async () => {
      if (user?.channelId) {
        try {
          const data = await getUserData(user.channelId.toString());
          if (data) {
            setUserData(data);
          }
        } catch (error) {
          console.error('Failed to fetch user data:', error);
        }
      }
    };
    
    fetchUserData();

    if (user?.email && Array.isArray(SUPERUSER_EMAILS)) {
      console.log('[Changelog] Setting superuser status for:', user.email);
      setIsSuperuser(SUPERUSER_EMAILS.includes(user.email));
    } else {
      console.log('[Changelog] Cannot determine superuser status:', { 
        hasEmail: Boolean(user?.email),
        hasSuperuserList: Array.isArray(SUPERUSER_EMAILS)
      });
      setIsSuperuser(false);
    }
    
    fetchNotices();
  }, [user?.email]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newMessage.trim()) return;

    console.log('[Changelog] Submitting new notice');
    setIsSubmitting(true);
    setError(null);
    
    try {
      await createNotice(newMessage, user.email);
      setNewMessage("");
      const updatedNotices = await getAllNotices();
      setNotices(updatedNotices);
      console.log('[Changelog] Notice created successfully');
    } catch (err) {
      console.error('[Changelog] Failed to create notice:', err);
      setError('Failed to create notice');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Add error display
  if (error) {
    console.log('[Changelog] Rendering error state:', error);
  }

  return (
    <Layout 
      user={user} 
      userData={userData}
      currentPath="changelog"
    >
      <div className="fixed inset-0 flex flex-col" style={{ paddingTop: 'env(safe-area-inset-top, 0)' }}>
        {/* Fixed Header */}
        <div className="flex-shrink-0 bg-[#1a1a1a] border-b border-gray-800">
          <div className="max-w-4xl mx-auto px-4 py-6">
            {/* Header */}
            <div className="relative flex items-center justify-center mb-4">
              <div 
                className="flex items-center gap-3 cursor-pointer group"
                onClick={() => globalThis.location.href = "/"}
              >
                <Logo size={32} />
                <span className="text-lg font-medium">awaken</span>
              </div>
            </div>
            {/* Page Title */}
            <h1 className="text-2xl font-semibold text-center">
              Updates & Changes
            </h1>
          </div>
        </div>
        
        {/* Scrollable Content */}
        <div 
          className="flex-1 overflow-y-auto overscroll-contain"
          style={{ 
            WebkitOverflowScrolling: 'touch',
            paddingBottom: 'env(safe-area-inset-bottom, 0)'
          }}
        >
          <div className="max-w-4xl mx-auto px-4 py-6">
            {error && (
              <div className="bg-red-500/10 border border-red-500 text-red-500 p-4 mb-4 rounded-lg">
                {error}
              </div>
            )}
            
            {/* Superuser Form */}
            {isSuperuser && (
              <form onSubmit={handleSubmit} className="mb-12">
                <div className="bg-[#1E1E1E] rounded-lg border border-[#FCA3114D] overflow-hidden">
                  <div className="p-4 border-b border-[#FCA3114D] bg-[#FCA311] bg-opacity-5">
                    <Text css="text-[#FCA311] font-medium">Create New Update</Text>
                  </div>
                  <div className="p-4">
                    <textarea
                      value={newMessage}
                      onChange={(e) => setNewMessage(e.target.value)}
                      placeholder="Write a new update message..."
                      className="w-full p-4 bg-black/30 rounded-lg text-white resize-none min-h-[120px] border border-[#FCA3114D] focus:border-[#FCA311] focus:outline-none transition-colors placeholder:text-gray-500"
                    />
                    <div className="mt-4 flex justify-end">
                      <Button
                        type="submit"
                        disabled={isSubmitting || !newMessage.trim()}
                        css="bg-[#FCA311] text-black hover:bg-[#FCA311]/90 disabled:opacity-50 rounded-lg transition-all duration-300 font-medium"
                      >
                        {isSubmitting ? "Posting..." : "Post Update"}
                      </Button>
                    </div>
                  </div>
                </div>
              </form>
            )}

            {/* Notice List */}
            <div className="space-y-4 pb-20">
              {notices.map((notice, index) => (
                <motion.div
                  key={notice.id || index}
                  className="bg-[#1E1E1E] p-6 rounded-lg border border-[#FCA3114D] hover:border-[#FCA311] transition-colors"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <div className="flex justify-between items-center mb-3">
                    <Text css="text-[#FCA311] text-sm font-medium">
                      {new Date(notice.Date || Date.now()).toLocaleDateString(undefined, {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      })}
                    </Text>
                    <Text css="text-gray-400 text-sm">
                      {new Date(notice.Date || Date.now()).toLocaleTimeString(undefined, {
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </Text>
                  </div>
                  <div className="text-white text-base leading-relaxed whitespace-pre-line">
                    {notice.Message || 'No message content'}
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
}