"use client";

import { useEffect, useRef, useState, useMemo, useCallback } from "npm:react@canary";
import { <PERSON><PERSON>, ScrollArea, Text } from "@reframe/ui/main.tsx";
import { CircleXIcon } from "@reframe/icons/circle-x.ts";
import { MessageItem } from "./MessageItem.tsx";
import { markCoachThreadMessagesSeenByUser, updateMessageStatus } from "../../../../actions/db/conversation-actions.ts";
import { sendUserCoachMessage, sendUserCoachVoiceMessage } from "../chat-actions.ts";
import { createPortal } from "npm:react-dom@canary";
import { animated, useTransition } from "npm:@react-spring/web@9.7.3";
import { getHumanCoachName, isAIOnlyCoach, getCoachDisplayName } from "../../components/coach-utils.ts";
import { VoiceInput } from "./VoiceInput.tsx";
import { MicIcon } from "../../../../lib/icons.tsx";

interface ThreadProps {
  visible: boolean;
  onClose: () => void;
  allMessages: any[]; // all messages, will be filtered internally
  initialMessageId: string | null;
  coachName: string;
  channelId: number | string;
  setAllMessages: (updater: (prev: any[]) => any[]) => void;
}

export const CoachThread: React.FC<ThreadProps> = ({
  visible,
  onClose,
  allMessages,
  initialMessageId,
  coachName,
  channelId,
  setAllMessages
}) => {
  // Don't render anything on the server
  if (typeof document === "undefined") return null;

  // Filter messages for this coach internally
  const messages = useMemo(() => {
    return allMessages.filter((m: any) => m.Type === "coach_message" && m.CoachName === coachName);
  }, [allMessages, coachName]);

  const [isRecordingMode, setIsRecordingMode] = useState(false);
  const [activeAudioId, setActiveAudioIdState] = useState<number | null>(null);

  // Wrap setActiveAudioId to add logging
  const setActiveAudioId = (id: number | null) => {
    console.log("[CoachThread] Setting activeAudioId to:", id);
    setActiveAudioIdState(id);
  };

  // Mark thread messages as seen when the thread becomes visible
  useEffect(() => {
    if (visible) {
      const hasUnseen = messages.some(m =>
        m.Sender !== "user" && (m.SeenByUser === 0 || m.SeenByUser === null || m.SeenByUser === undefined)
      );
      if (hasUnseen) {
        markCoachThreadMessagesSeenByUser(channelId, coachName).catch(err => console.error("Failed to mark coach thread messages seen", err));
      }
    } else {
      // Stop any playing audio when thread closes
      setActiveAudioId(null);
    }
  }, [visible, messages, channelId, coachName]);
  
  // Messages are now passed directly from parent, no need for local state
  
  const [input, setInput] = useState("");
  const scrollRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Scroll to the highlighted message when thread opens or messages update
  useEffect(() => {
    if (!scrollRef.current) return;
    const scrollArea = scrollRef.current;

    if(initialMessageId && isHighlightEnabled) {
      // Slight delay ensures elements & animations are ready
      const timer = setTimeout(() => {
        const el = scrollArea.querySelector(`[data-thread-id="${initialMessageId}"]`) as HTMLElement | null;
        if (el) {
          el.scrollIntoView({ block: "center" });
        }
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [initialMessageId, messages]);

  useEffect(() => {
    if (visible && (!initialMessageId || !isHighlightEnabled) && scrollRef.current) {
      const scrollArea = scrollRef.current;
      // Use requestAnimationFrame to scroll after render
      requestAnimationFrame(() => {
        scrollArea.scrollTop = scrollArea.scrollHeight;
      });
    }
  }, [visible, initialMessageId, messages])

  // Auto-resize textarea on input change
  useEffect(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      const newHeight = Math.min(textarea.scrollHeight, 150); // Max height of 150px
      textarea.style.height = `${newHeight}px`;
    }
  }, [input]);

  // Handle enter key to send message (shift+enter for new line)
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    // Check if device is mobile
    const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent) || window.innerWidth <= 768;
    
    if (e.key === 'Enter') {
      if (isMobile) {
        // On mobile: Enter = new line, Shift+Enter = send
        if (e.shiftKey) {
          e.preventDefault();
          if (input.trim()) {
            handleSend();
          }
        }
        // Let Enter create new line naturally (don't prevent default)
      } else {
        // On desktop: Enter = send, Shift+Enter = new line
        if (!e.shiftKey) {
          e.preventDefault();
          if (input.trim()) {
            handleSend();
          }
        }
        // Let Shift+Enter create new line naturally
      }
    }
  };

  // Highlight state for initial message highlight ring (no pulse)
  const [highlightActive, setHighlightActive] = useState(!!initialMessageId);
  const [isHighlightEnabled, setIsHighlightEnabled] = useState(true);

  useEffect(() => {
    if (initialMessageId) {
      const t = setTimeout(() => setHighlightActive(false), 2000);
      return () => clearTimeout(t);
    }
  }, [initialMessageId]);

  // Slide-in/out animation optimized for smoothness
  const slideTransition = useTransition(visible, {
    from: {
      transform: "translateY(100%)",
      opacity: 0,
    },
    enter: {
      transform: "translateY(0%)",
      opacity: 1,
      delay: 0, // Remove delay to prevent stuttering
    },
    leave: {
      transform: "translateY(100%)",
      opacity: 0, // Fade out the entire overlay quickly
      delay: 0, // No delay for exit
    },
    config: (key) => {
      // Use different configs for different properties
      if (key === 'opacity') {
        return {
          duration: visible ? 200 : 150, // Slightly longer fade in, fast fade out
          easing: (t) => t * t, // Ease-in for smoother start
        };
      }
      return {
        mass: 0.8,    // Reduced mass for snappier response
        tension: 400, // Higher tension for more responsive animation
        friction: 30, // Reduced friction for smoother motion
        clamp: true,  // Prevent overshoot/bounce
      };
    },
  });

  const handleSend = async () => {
    const trimmed = input.trim();
    if (!trimmed) return;

    if (isHighlightEnabled) {
      setIsHighlightEnabled(false);
    }

    try {
      console.log("[CoachThread] Sending message:", trimmed);
      const saved = await sendUserCoachMessage(channelId, coachName, trimmed);
      console.log("[CoachThread] Message saved:", saved);
      if (saved) {
        const newMessage = {
          Id: saved.id,
          Content: trimmed, // Use the original trimmed content since it was successfully saved
          Date: saved.fields.Date,
          Sender: saved.fields.Sender,
          Type: saved.fields.Type,
          CoachName: saved.fields.CoachName,
          Status: saved.fields.Status,
          Audio: saved.fields.Audio,
        };

        // Update global allMessages state (local messages will be updated via props)
        setAllMessages((prev: any[]) => [...prev, newMessage]);

        setInput("");
        requestAnimationFrame(() => {
          if (scrollRef.current) {
            scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
          }
        });
      }
    } catch (e) {
      console.error("sendUserCoachMessage error", e);
    }
  };

  // Toggle star status for a message
  const toggleStar = useCallback(async (msgId: string, currentStatus = "Default") => {
    // Skip if no message ID or it's a temporary ID
    if (!msgId || msgId.startsWith("temp-")) {
      console.log("[CoachThread] Cannot star message: Invalid or temporary ID");
      return;
    }

    const newStatus = currentStatus === "Starred" ? "Default" : "Starred";

    try {
      await updateMessageStatus(msgId, newStatus);

      // Update global state for immediate UI feedback
      setAllMessages(prev =>
        prev.map(m => (m.Id === msgId ? { ...m, Status: newStatus } : m))
      );
    } catch (error) {
      console.error("[CoachThread] Error toggling star:", error);
    }
  }, [setAllMessages]);

  // Wrap the mapping in useMemo to prevent recreating the list JSX when unrelated state changes
  const messageList = useMemo(() => {
    return messages.map((m, idx) => {
      const isUser = m.Sender === "user";
      const isHighlight = highlightActive && isHighlightEnabled && m.Id === initialMessageId;

      return (
        <div
          key={m.Id}
          data-thread-id={m.Id}
          className={`flex flex-col w-full ${isUser ? "items-end" : "items-start"} mb-3`}
        >
          <div className="w-full max-w-[90%]">
            <MessageItem
              message={m}
              index={idx}
              allMessages={messages}
              contentReady={true}
              currentLetterIndex={0}
              activeAudioId={activeAudioId}
              setActiveAudioId={setActiveAudioId}
              toggleStar={toggleStar}
              showCoachPill={false}
              highlight={isHighlight}
              showHumanCoachName={true}
              useContextualDateSeparators={true}
            />
          </div>
        </div>
      );
    });
  }, [messages, highlightActive, isHighlightEnabled, initialMessageId, activeAudioId, setActiveAudioId, toggleStar]);

  // Handle voice recording completion
  const handleVoiceComplete = async (audioBlob: Blob) => {
    if (isHighlightEnabled) {
      setIsHighlightEnabled(false);
    }

    const tempId = `temp-${Date.now()}`;
    const audioUrl = URL.createObjectURL(audioBlob);

    try {
      // Optimistically add message with local audio URL
      
      const optimisticMessage = {
        Id: tempId,
        Content: "Transcribing...", // Show transcribing status
        Date: new Date().toISOString(),
        Sender: "user",
        Type: "coach_message",
        CoachName: coachName,
        Audio: audioUrl,
        Status: "Default",
        isTemporary: true,
      };

      // Update global state (local messages will be updated via props)
      setAllMessages((prev) => [...prev, optimisticMessage]);
      
      console.log("[CoachThread] Optimistic audio URL:", audioUrl);

      // Send to server
      const saved = await sendUserCoachVoiceMessage(channelId, coachName, audioBlob);
      
      if (saved) {
        // Replace temporary message with actual saved message
        console.log("[CoachThread] Saved message audio URL:", saved.fields.Audio);
        console.log("[CoachThread] Full saved object:", saved);
        // Create properly formatted message object
        // The transcript is saved as the content in the database
        const savedContent = (saved.fields as any).Content || "[Voice message]";
        const formattedMessage = {
          // Lowercase properties (for potential compatibility)
          id: saved.id,
          channelId: Number(channelId),
          sender: "user",
          content: savedContent,
          audio: saved.fields.Audio,
          date: saved.fields.Date,
          status: saved.fields.Status,
          messageType: "coach_message",
          coachName: coachName,
          // Uppercase properties (what MessageItem expects)
          Id: saved.id,
          Sender: "user",
          Content: savedContent,
          Audio: saved.fields.Audio,
          Date: saved.fields.Date,
          Status: saved.fields.Status,
          Type: "coach_message",
          CoachName: coachName,
        };
        
        // Replace optimistic with real saved row in global state
        setAllMessages((prev) =>
          prev.map((m) => (m.Id === tempId || m.id === tempId ? formattedMessage : m))
        );
        
        // Clean up the temporary blob URL
        URL.revokeObjectURL(audioUrl);
        
        // Scroll to bottom after adding message
        requestAnimationFrame(() => {
          if (scrollRef.current) {
            scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
          }
        });
      }
    } catch (e) {
      console.error("sendUserCoachVoiceMessage error", e);
      // Remove the temporary message on error from global state
      setAllMessages((prev) => prev.filter(msg => !msg.isTemporary));
      // Clean up the temporary blob URL on error
      URL.revokeObjectURL(audioUrl);
    }
  };

  const overlay = slideTransition((style, item) =>
    item ? (
      <animated.div
        className="fixed inset-0 z-50 flex items-end justify-center bg-black/70 sm:pt-4 sm:px-4"
        style={{
          opacity: style.opacity,
        }}
        onClick={onClose}
      >
        <animated.div
          style={{
            transform: style.transform,
            willChange: "transform, opacity",
            backfaceVisibility: "hidden",
            perspective: 1000,
          }}
          onClick={(e) => e.stopPropagation()}
          className="w-full sm:max-w-3xl h-[85vh] bg-gradient-to-b from-[#0B0B0B] via-[#161616] to-[#0B0B0B] backdrop-blur-md border-t sm:border-l sm:border-r border-[#FCA31180] rounded-t-3xl flex flex-col"
        >
        {/* Header */}
        <div className="flex items-center justify-between pl-4 pr-1 py-4 border-b border-white/10 max-w-3xl mx-auto w-full">
          <Text className="text-white font-semibold">From {isAIOnlyCoach(coachName) ? "Awaken's creators" : `the actual ${getHumanCoachName(coachName)}`}</Text>
          <Button
            variant="ghost"
            css="hover:bg-white/10 rounded-lg"
            onClick={onClose}
          >
            <CircleXIcon size={28} className="text-white" />
          </Button>
        </div>

        {/* Body */}
        <ScrollArea
          className="flex-1 overflow-y-auto p-4 space-y-4 w-full max-w-3xl mx-auto"
          ref={scrollRef}
        >
          {messageList}
        </ScrollArea>

        {/* Input */}
        <div className="border-t border-white/10 p-4 max-w-3xl mx-auto w-full">
          {isRecordingMode ? (
            <VoiceInput
              channelId={String(channelId)}
              isRecordingMode={isRecordingMode}
              setIsRecordingMode={setIsRecordingMode}
              onRecordingComplete={handleVoiceComplete}
            />
          ) : (
            <div className="flex gap-3">
              <textarea
                ref={textareaRef}
                className="flex-1 bg-white/10 text-white placeholder-gray-400 border border-white/20 rounded-lg px-4 py-2 focus:outline-none focus:border-orange-500 focus:ring-1 focus:ring-orange-500 resize-none overflow-y-auto"
                placeholder={`Reply to ${getCoachDisplayName(coachName)}...`}
                rows={1}
                style={{
                  minHeight: "48px",
                  maxHeight: "150px",
                }}
                value={input}
                onChange={(e) => setInput(e.target.value)}
                onKeyDown={handleKeyDown}
                disabled={isRecordingMode}
              />
              <Button
                area-label="voice-record"
                variant="outline"
                css="h-[48px] w-[48px] rounded-md border-none bg-white/10 hover:bg-white/20 flex items-center justify-center"
                onClick={() => setIsRecordingMode(true)}
              >
                <MicIcon className="w-6 h-6 text-[#FCA311]" />
              </Button>
              <Button
                onClick={handleSend}
                css={`${
                  input.trim() === "" ? "bg-white/10 text-gray-400 cursor-not-allowed" : "bg-orange-600 hover:bg-orange-700 text-white"} px-4 py-2 rounded-lg`}
                disabled={input.trim() === "" || isRecordingMode}
              >
                Send
              </Button>
            </div>
          )}
        </div>
        </animated.div>
      </animated.div>
    ) : null
  );

  return createPortal(overlay, document.body);
}; 