"use client";

import React from "npm:react@canary";
import { Button } from "@reframe/ui/main.tsx";
import { format } from "npm:date-fns";
import { MESSAGE_TYPES, isCallMessage } from '../../../../lib/message-types.ts';

// Define proper message type instead of using 'any'
interface Message {
  Id?: string;
  isTemporary?: boolean;
  Status?: string;
  Sender?: string;
  Content?: string;
  Audio?: string;
  Date?: string;
  CoachName?: string;
}

interface MessageBubbleProps {
  isUser: boolean;
  children: React.ReactNode;
  contentReady: boolean;
  highlight?: boolean;
}

interface AwakeningBubbleProps {
  children: React.ReactNode;
  message: Message; 
  toggleStar: (id: string, status: string) => void;
  contentReady: boolean;
}

const MessageBubble = React.memo(({ isUser, children, contentReady, highlight = false }: MessageBubbleProps) => {
  const userStyles = "bg-[#5b3109]/60 border border-amber-500/30 text-amber-50";
  const otherStyles = "bg-[#3c1c07]/60 border border-amber-700/30 text-amber-100";
  const highlightStyles = highlight ? "ring-2 ring-[#FCA311]" : "";

  return (
    <div
      className={`rounded-2xl px-4 py-3 message-bubble ${isUser ? userStyles : otherStyles} ${highlightStyles}`}
      style={{
        willChange: 'transform, opacity',
        opacity: contentReady ? 1 : 0,
        transition: 'opacity 200ms ease-out, background-color 200ms ease-out, border-color 200ms ease-out, color 200ms ease-out',
        width: 'fit-content',
      }}
    >
      {children}
    </div>
  );
});

const AwakeningBubble = React.memo(({ children, message, toggleStar, contentReady }: AwakeningBubbleProps) => {
  return (
    <div 
      className="rounded-2xl px-4 py-3 inline-block w-full relative overflow-hidden"
      style={{ 
        willChange: 'transform, opacity',
        backgroundColor: '#331601',
        color: '#FCA311',
        borderLeft: '3px solid #FCA311',
        opacity: contentReady ? 1 : 0,
        transition: 'opacity 200ms ease-out, background-color 200ms ease-out',
      }}
    >
      <div 
        className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-[#FF5727] to-[#FFC360]"
        style={{
          opacity: contentReady ? 0.7 : 0,
          transition: 'opacity 200ms ease-out',
        }}
      />
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center">
          <div className="w-5 h-5 text-[#FCA311] mr-2">✨</div>
          <span className="font-semibold text-[#FCA311]">Daily Awakening</span>
        </div>
        
        {/* Star button for awakening */}
        {!message.isTemporary && message.Id && !message.Id.startsWith('temp-') && (
          <Button
            variant="outline"
            css="w-7 h-7 p-1 rounded-full border-[#505050] hover:bg-black hover:border-[#505052] flex items-center justify-center"
            onClick={() => toggleStar(message.Id || '', message.Status || "Default")}
          >
            <div className={`h-3.5 w-3.5 ${
              message.Status === "Starred"
                ? "text-[#FCA311]"
                : "text-[#9B9B9B]"
            }`}>
              {message.Status === "Starred" ? "★" : "☆"}
            </div>
          </Button>
        )}
      </div>
      {children}
    </div>
  );
});

interface StarButtonProps {
  message: Message;
  toggleStar: (id: string, status: string) => void;
  position: "left" | "right";
}

const StarButton = ({ message, toggleStar, position }: StarButtonProps) => {
  if (!message.Id || message.isTemporary || (message.Id && message.Id.startsWith('temp-'))) {
    return null;
  }
  
  return (
    <Button
      variant="outline"
      css={`w-7 h-7 p-1 rounded-full border-[#505050] hover:bg-black hover:border-[#505052] flex items-center justify-center self-center flex-shrink-0 ${position === "left" ? "mr-1" : "ml-1"}`}
      onClick={() => toggleStar(message.Id || '', message.Status || "Default")}
    >
      <div className={`h-3.5 w-3.5 ${
        message.Status === "Starred"
          ? "text-[#FCA311]"
          : "text-[#9B9B9B]"
      }`}>
        {message.Status === "Starred" ? "★" : "☆"}
      </div>
    </Button>
  );
};

interface MessageMetaProps {
  sender: string;
  date: string | Date;
  coachName?: string;
}

const MessageMeta = ({ sender, date, coachName = "Kokoro" }: MessageMetaProps) => {
  const isUser = sender === "user";
  const timestampColor = isUser ? "text-amber-200/70" : "text-amber-400/70";

  return (
    <div className="flex items-center mb-1 px-1">
      <span className="text-[#fcb645] text-[10px]">
        {isUser ? "You" : coachName}
      </span>
      <span className={`${timestampColor} text-[10px] ml-2`}>
        {date
          ? format(new Date(date), "h:mm a")
          : "Invalid time"}
      </span>
    </div>
  );
};

export { MessageBubble, AwakeningBubble, StarButton, MessageMeta };
