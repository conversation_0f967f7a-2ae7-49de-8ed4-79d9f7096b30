import { saveMessage, uploadAudio, generateTranscription } from "../../../actions/db/conversation-actions.ts";
import { v4 as uuidv4 } from "npm:uuid";
import { sendUserToCoachMessageNotification } from "../../../actions/push-notifications/push-notification-service.ts";

/**
 * Send a plain-text message from the user to the coach inside a coach thread.
 */
export const sendUserCoachMessage = async (
  channelId: string | number,
  coachName: string,
  content: string,
) => {
  console.log("HERE 1");
  const trimmed = content.trim();
  if (!trimmed) return null;

  const saved = await saveMessage(
    channelId,
    "user",
    trimmed,
    undefined,           // date defaults to now()
    "Default",
    "coach_message",     // keeps messages scoped to coach threads
    coachName,
    null,                // no audio URL
  );

  console.log("HERE 2");
  // Send push notification to coach
  if (saved) {
    try {
      sendUserToCoachMessageNotification({
        userChannelId: channelId,
        coachName: coachName,
        messageContent: trimmed,
        messageId: saved.id,
      });
      console.log(`[USER_COACH_MESSAGE] Push notification sent to coach ${coachName} for message from user ${channelId}`);
    } catch (error) {
      console.error(`[USER_COACH_MESSAGE] Failed to send push notification to coach:`, error);
      // Don't fail the message sending if push notification fails
    }
  }
  console.log("HERE 3");
  return saved;
};

/**
 * Send a VOICE message from the user to the coach inside a coach thread.
 *
 * 1. Uploads the audio Blob to GCS (via uploadAudio).
 * 2. Attempts to obtain an automatic transcription (non-blocking).
 * 3. Persists the conversation row with a reference to the audio URL.
 *
 * Returns the saved row so the caller can merge it into UI state.
 */
export const sendUserCoachVoiceMessage = async (
  channelId: string | number,
  coachName: string,
  audioBlob: Blob,
) => {
  // Generate a deterministic id for filename & DB correlation
  const messageId = uuidv4();

  // 1. Upload audio and grab the public URL
  const audioUrl = await uploadAudio(audioBlob, String(channelId), messageId);

  // 2. Try to transcribe – failure is non-fatal
  let transcript = "";
  try {
    transcript = await generateTranscription(audioBlob, "deepgram");
    // Deepgram returns an object with { text: "..." }
    if (transcript && typeof transcript === 'object' && 'text' in transcript) {
      transcript = transcript.text;
    }
  } catch (err) {
    console.warn("[sendUserCoachVoiceMessage] transcription failed – continuing without it", err);
  }

  // 3. Persist the message
  const saved = await saveMessage(
    channelId,
    "user",
    transcript,
    undefined,           // timestamp = now
    "Default",
    "coach_message",     // same thread namespace
    coachName,
    audioUrl,
  );

  // Send push notification to coach
  if (saved) {
    try {
      await sendUserToCoachMessageNotification({
        userChannelId: channelId,
        coachName: coachName,
        messageContent: transcript || "[Voice message]",
        messageId: saved.id,
      });
      console.log(`[USER_COACH_VOICE_MESSAGE] Push notification sent to coach ${coachName} for voice message from user ${channelId}`);
    } catch (error) {
      console.error(`[USER_COACH_VOICE_MESSAGE] Failed to send push notification to coach:`, error);
      // Don't fail the message sending if push notification fails
    }
  }

  return saved;
};