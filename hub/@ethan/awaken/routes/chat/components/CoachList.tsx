"use client";

import React, { useState, useEffect } from "npm:react@canary";
import { ScrollArea, Text } from "@reframe/ui/main.tsx";
import { Logo } from "../../../lib/logo.tsx";
import { setSelectedCoach } from "./coach-utils.ts";
import { markMessagesSeenByUser } from "../../../actions/db/conversation-actions.ts";

interface CoachListItem {
  name: string;
  lastMessage?: {
    content: string;
    date: string;
    sender: string;
    type: string;
  };
  hasUnseen?: boolean;
  avatar?: string;
}

interface CoachListProps {
  coaches: string[];
  selectedCoach?: string | null;
  onCoachSelect: (coachName: string) => void;
  allMessages: any[];
  user: any;
  setAllMessages: (updater: (prev: any[]) => any[]) => void;
}

// Function to convert coach name to portrait image URL
const getCoachPortraitUrl = (coachName: string): string => {
  const fileName = coachName.toLowerCase().replace(/\s+/g, '-') + '-portrait.png';
  return `https://storage.googleapis.com/awaken-audio-files/${fileName}`;
};

export const CoachList: React.FC<CoachListProps> = ({ 
  coaches, 
  selectedCoach, 
  onCoachSelect, 
  allMessages,
  user,
  setAllMessages
}) => {
  const [coachListItems, setCoachListItems] = useState<CoachListItem[]>([]);
  const [isChangingCoach, setIsChangingCoach] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  useEffect(() => {
    // Process coaches and their last messages
    const processedCoaches = coaches.map(coachName => {
      // Filter messages for this coach
      const coachMessages = allMessages.filter(msg => msg.CoachName === coachName);
      
      // Get the most recent message
      const lastMessage = coachMessages.length > 0 
        ? coachMessages[coachMessages.length - 1] // Messages are sorted oldest first
        : undefined;

      // Determine if there are unseen messages from the coach
      const hasUnseen = coachMessages.some(m =>
        // Unread messages are those sent by the coach / assistant and not yet seen by the user
        m.Sender !== "user" && (m.SeenByUser === 0 || m.SeenByUser === null || m.SeenByUser === undefined)
      );
 
      return {
        name: coachName,
        lastMessage: lastMessage ? {
          content: lastMessage.Content,
          date: lastMessage.Date,
          sender: lastMessage.Sender,
          type: lastMessage.Type
        } : undefined,
        hasUnseen,
        avatar: getCoachPortraitUrl(coachName)
      };
    });

    // Sort coaches by the most recent message date (descending)
    processedCoaches.sort((a, b) => {
      const dateA = a.lastMessage ? new Date(a.lastMessage.date).getTime() : 0;
      const dateB = b.lastMessage ? new Date(b.lastMessage.date).getTime() : 0;
      return dateB - dateA;
    });

    setCoachListItems(processedCoaches);
  }, [coaches, allMessages]);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 168) { // Less than a week
      return date.toLocaleDateString([], { weekday: 'short' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  const truncateMessage = (message: string, maxLength: number = 40) => {
    if (message.length <= maxLength) return message;
    return message.substring(0, maxLength) + '...';
  };

  const handleCoachClick = async (coachName: string) => {
    if (isChangingCoach && selectedCoach === coachName) return;

    const coachMessages = allMessages.filter(m => m.CoachName === coachName);
    const unseenConversation = coachMessages.some(m =>
      m.Sender !== "user" && (m.SeenByUser === 0 || m.SeenByUser === null || m.SeenByUser === undefined) && m.Type !== "coach_message"
    );
    const unseenThread = coachMessages.some(m =>
      m.Sender !== "user" && (m.SeenByUser === 0 || m.SeenByUser === null || m.SeenByUser === undefined) && m.Type === "coach_message"
    );

    onCoachSelect(coachName);

    if (unseenConversation) {
      // Optimistically update UI: red dot remains if thread messages are still unseen
      setCoachListItems(prev => prev.map(item =>
        item.name === coachName ? { ...item, hasUnseen: unseenThread } : item
      ));

      // Optimistically mark regular conversation messages as seen in allMessages
      setAllMessages(prev => prev.map(m =>
        m.CoachName === coachName && 
        m.Sender !== "user" && 
        m.Type !== "coach_message" && 
        (m.SeenByUser === 0 || m.SeenByUser === null || m.SeenByUser === undefined)
          ? { ...m, SeenByUser: 1 }
          : m
      ));

      // Mark only normal conversation messages as seen
      markMessagesSeenByUser(user.channelId, coachName).catch(err => console.error("Failed to mark messages as seen", err));
    }

    setIsChangingCoach(true);
    setSelectedCoach(user.channelId, coachName, true)
      .catch(error => {
        console.error('Failed to persist coach preference:', error);
      })
      .finally(() => setIsChangingCoach(false));
  };

  return (
    <div className="h-full bg-black/20 backdrop-blur-sm border-r border-white/10">
      {/* Header */}
      <div className="p-4 border-b border-white/10">
        <div className="flex items-center justify-center gap-3">
          <Logo size={32} />
          <span className="text-lg font-medium">awaken</span>
        </div>
      </div>

      {/* Error Message */}
      {errorMessage && (
        <div className="px-4 py-2 bg-red-500/10 border-b border-red-500/20">
          <Text className="text-sm text-red-400">{errorMessage}</Text>
        </div>
      )}

      {/* Coach List */}
      <ScrollArea className="h-full">
        <div className="divide-y divide-white/5 w-full">
          {coachListItems.map((coach) => (
            <div
              key={coach.name}
              onClick={() => handleCoachClick(coach.name)}
              className={`p-4 bg-transparent hover:bg-white/5 cursor-pointer transition-all duration-200 hover:translate-x-1 ${isChangingCoach ? 'opacity-50 cursor-wait' : ''}`}
            >
              <div className="flex items-start gap-3 w-full">
                {/* Coach Avatar */}
                <div className="flex-shrink-0 relative">
                  <img
                    src={coach.avatar}
                    alt={coach.name}
                    className="w-12 h-12 rounded-full object-cover border-2 border-white/10 shadow-lg"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      // Prevent infinite loop if fallback image also fails
                      if (!target.src.endsWith('/default-coach-avatar.png')) {
                        target.src = '/default-coach-avatar.png';
                      }
                    }}
                  />
                  {coach.hasUnseen && (
                    <div className="absolute -top-1 -right-1 w-4 h-4 bg-orange-500 rounded-full border-2 border-black"></div>
                  )}
                </div>

                {/* Coach Info */}
                <div className="flex-1 min-w-0">
                  {/* Coach Name and Time */}
                  <div className="flex items-center justify-between mb-1">
                    <Text className="font-medium text-white truncate">
                      {coach.name}
                    </Text>
                    {coach.lastMessage && (
                      <Text className="text-xs text-gray-400 flex-shrink-0 ml-2">
                        {formatDate(coach.lastMessage.date)}
                      </Text>
                    )}
                  </div>

                  {/* Last Message Preview */}
                  {coach.lastMessage ? (
                    <div className="flex items-center gap-2 mb-2">
                      {coach.lastMessage.type === 'coach_message' && coach.lastMessage.sender === 'coach' && (
                        <div className="w-4 h-4 bg-blue-500/20 rounded-full flex items-center justify-center flex-shrink-0">
                          <span className="text-blue-400 text-xs">👤</span>
                        </div>
                      )}
                      <Text className="text-sm text-gray-300 leading-relaxed flex-1 min-w-0">
                        {truncateMessage(coach.lastMessage.content)}
                      </Text>
                    </div>
                  ) : (
                    <Text className="text-sm text-gray-400 italic">
                      Ready for your first message
                    </Text>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </ScrollArea>
    </div>
  );
};
