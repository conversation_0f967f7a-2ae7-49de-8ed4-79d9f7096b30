"use server";

// Core gift essence functionality
import { db } from "../../lib/db.ts";
import { sql } from "npm:kysely";
import { verifyUserPermission } from "../../lib/auth-helper.ts";

/**
 * Generates a unique 8-character gift code using Crockford Base-32
 * Excludes ambiguous characters (0, O, I, L) for better UX
 */
export const generateGiftCode = (): string => {
  const chars = '0123456789ABCDEFGHJKMNPQRSTVWXYZ'; // Crockford Base-32 alphabet
  let code = '';
  for (let i = 0; i < 8; i++) {
    code += chars[Math.floor(Math.random() * chars.length)];
  }
  return code;
};

/**
 * Creates a new gift record after successful payment
 * @param data Gift data from Stripe checkout
 * @returns The created gift record
 */
export const createGiftRow = async (data: {
  code: string;
  essenceAmount: number;
  purchaserEmail: string;
  recipientEmail: string;
  senderName?: string;
  message?: string;
  stripePaymentIntentId: string;
}) => {
  try {
    console.log("[GIFT DB] Inserting gift record:", data);
    
    const gift = await db
      .insertInto('gift_codes')
      .values({
        code: data.code,
        essence_amount: data.essenceAmount,
        purchaser_email: data.purchaserEmail,
        recipient_email: data.recipientEmail,
        sender_name: data.senderName,
        message: data.message,
        stripe_payment_intent_id: data.stripePaymentIntentId,
      })
      .returningAll()
      .executeTakeFirst();

    console.log("[GIFT DB] Gift record created successfully:", gift);
    return gift;
  } catch (error) {
    console.error("[GIFT DB] Error creating gift record:", error);
    throw error;
  }
};

/**
 * Core function to apply a gift to a user's balance
 * Single source of truth for gift redemption
 * @param code The gift code to redeem
 * @param channelId The recipient's channel ID (if known)
 * @returns Success status and updated balance
 */
export const applyGift = async (code: string, channelId?: number) => {
  try {
    const result = await db.transaction().execute(async (trx) => {
      // Get the gift (SQLite handles locking automatically in transactions)
      const gift = await trx
        .selectFrom('gift_codes')
        .selectAll()
        .where('code', '=', code)
        .where('applied_at', 'is', null)
        .executeTakeFirst();

      if (!gift) {
        throw new Error('Invalid or already used gift code');
      }

      // If no channelId provided, gift remains pending
      if (!channelId) {
        return { success: true, pending: true, amount: gift.essenceAmount };
      }

      // Mark gift as applied
      await trx
        .updateTable('gift_codes')
        .set({
          appliedChannelId: channelId,
          appliedAt: sql`CURRENT_TIMESTAMP`,
        })
        .where('code', '=', code)
        .where('appliedAt', 'is', null)
        .execute();

      // Credit the user's balance
      const updatedUser = await trx
        .updateTable('user')
        .set({
          addedEssenceBalance: sql`added_essence_balance + ${Number(gift.essenceAmount)}`,
        })
        .where('channelId', '=', channelId)
        .returning(['addedEssenceBalance', 'monthlyEssenceBalance'])
        .executeTakeFirst();

      if (!updatedUser) {
        throw new Error('User not found');
      }

      const totalBalance = updatedUser.addedEssenceBalance + updatedUser.monthlyEssenceBalance;

      return {
        success: true,
        amount: gift.essenceAmount,
        balance: totalBalance,
        senderName: gift.senderName,
        message: gift.message,
      };
    });

    return result;
  } catch (error) {
    console.error('[GIFT] Error applying gift:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to apply gift',
    };
  }
};

/**
 * Gets a gift by code (for validation/display purposes)
 * @param code The gift code
 * @returns Gift details or null
 */
export const getGiftByCode = async (code: string) => {
  const gift = await db
    .selectFrom('gift_codes')
    .selectAll()
    .where('code', '=', code)
    .executeTakeFirst();

  return gift;
};

/**
 * Checks if a user has pending gifts by email
 * Used during sign-up to auto-credit gifts
 * @param email User's email address
 * @returns Array of pending gifts
 */
export const getPendingGiftsByEmail = async (email: string) => {
  const gifts = await db
    .selectFrom('gift_codes')
    .selectAll()
    .where('recipient_email', '=', email.toLowerCase())
    .where('applied_at', 'is', null)
    .execute();

  return gifts;
};

/**
 * Auto-credits all pending gifts for a user
 * Called after user creation or sign-in
 * @param email User's email
 * @param channelId User's channel ID
 * @returns Object with total essence credited and gift details
 */
export const autoCreditPendingGifts = async (email: string, channelId: number) => {
  const pendingGifts = await getPendingGiftsByEmail(email);
  let totalCredited = 0;
  const giftDetails: Array<{amount: number, senderName?: string, message?: string}> = [];

  for (const gift of pendingGifts) {
    const result = await applyGift(gift.code, channelId);
    if (result.success && !result.pending) {
      totalCredited += result.amount || 0;
      giftDetails.push({
        amount: result.amount || 0,
        senderName: result.senderName,
        message: result.message
      });
    }
  }

  return {
    totalCredited,
    gifts: giftDetails
  };
};