"use server";

import { getActivePushTokens } from "./push-token-actions.ts";

export interface PushNotificationPayload {
  to: string;
  title: string;
  body: string;
  data?: Record<string, any>;
  sound?: string;
  badge?: number;
  priority?: 'default' | 'normal' | 'high';
  channelId?: string;
}

export interface CoachMessageNotificationData {
  channelId: string | number;
  coachName: string;
  messageContent: string;
  messageId?: string;
}

export interface UserToCoachMessageNotificationData {
  userChannelId: string | number;
  coachName: string;
  messageContent: string;
  messageId?: string;
  userName?: string;
}

/**
 * Send push notification using Expo's push notification service
 */
export const sendExpoPushNotification = async (
  payload: PushNotificationPayload
): Promise<{ success: boolean; error?: string; receipt?: any }> => {
  try {
    console.log(`[PUSH_NOTIFICATION] Sending notification to ${payload.to}`);

    const response = await fetch('https://exp.host/--/api/v2/push/send', {
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'Accept-encoding': 'gzip, deflate',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        to: payload.to,
        title: payload.title,
        body: payload.body,
        data: payload.data || {},
        sound: payload.sound || 'default',
        badge: payload.badge,
        priority: payload.priority || 'high',
        channelId: payload.channelId || 'default',
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`[PUSH_NOTIFICATION] Failed to send notification: ${response.status} ${errorText}`);
      return { success: false, error: `HTTP ${response.status}: ${errorText}` };
    }

    const result = await response.json();
    console.log(`[PUSH_NOTIFICATION] Notification sent successfully:`, result);

    return { success: true, receipt: result };
  } catch (error) {
    console.error("[PUSH_NOTIFICATION] Error sending push notification:", error);
    return { success: false, error: error.message };
  }
};

/**
 * Send push notification to all active devices for a user when they receive a coach message
 */
export const sendCoachMessageNotification = async (
  data: CoachMessageNotificationData
): Promise<{ success: boolean; sentCount: number; errors: string[] }> => {
  try {
    console.log(`[COACH_MESSAGE_NOTIFICATION] Sending notification for coach message from ${data.coachName} to channel ${data.channelId}`);
    
    // Get all active push tokens for the user
    const tokens = await getActivePushTokens(data.channelId);
    
    if (tokens.length === 0) {
      console.log(`[COACH_MESSAGE_NOTIFICATION] No active push tokens found for channel ${data.channelId}`);
      return { success: true, sentCount: 0, errors: [] };
    }

    console.log(`[COACH_MESSAGE_NOTIFICATION] Found ${tokens.length} active tokens for channel ${data.channelId}`);

    // Prepare notification payload
    const title = `Message from ${data.coachName}`;
    const body = data.messageContent.length > 100 
      ? `${data.messageContent.substring(0, 97)}...`
      : data.messageContent;

    const notificationData = {
      type: 'coach_message',
      coachName: data.coachName,
      channelId: data.channelId.toString(),
      messageId: data.messageId,
    };

    // Send notifications to all tokens
    const results = await Promise.allSettled(
      tokens.map(token =>
        sendExpoPushNotification({
          to: token.token,
          title,
          body,
          data: notificationData,
          sound: 'default',
          priority: 'high',
          channelId: 'coach-messages',
        })
      )
    );

    // Process results
    let sentCount = 0;
    const errors: string[] = [];

    results.forEach((result, index) => {
      if (result.status === 'fulfilled' && result.value.success) {
        sentCount++;
      } else {
        const error = result.status === 'rejected' 
          ? result.reason 
          : result.value.error;
        errors.push(`Token ${tokens[index].id}: ${error}`);
        console.error(`[COACH_MESSAGE_NOTIFICATION] Failed to send to token ${tokens[index].id}:`, error);
      }
    });

    console.log(`[COACH_MESSAGE_NOTIFICATION] Sent ${sentCount}/${tokens.length} notifications successfully`);
    
    if (errors.length > 0) {
      console.error(`[COACH_MESSAGE_NOTIFICATION] Errors:`, errors);
    }

    return { 
      success: sentCount > 0 || tokens.length === 0, 
      sentCount, 
      errors 
    };

  } catch (error) {
    console.error("[COACH_MESSAGE_NOTIFICATION] Error sending coach message notification:", error);
    return {
      success: false,
      sentCount: 0,
      errors: [error.message]
    };
  }
};

/**
 * Send push notification to a coach when they receive a message from a user
 */
export const sendUserToCoachMessageNotification = async (
  data: UserToCoachMessageNotificationData
): Promise<{ success: boolean; sentCount: number; errors: string[] }> => {
  try {
    console.log(`[USER_TO_COACH_NOTIFICATION] Sending notification for user message to coach ${data.coachName} from user ${data.userChannelId}`);

    // Import utilities
    const { db } = await import("../../lib/db.ts");
    const { getUserInfo } = await import("../../lib/get-user-info.ts");

    // First, get the coach's email from the coaches table
    const coach = await db
      .selectFrom("coaches")
      .select(["email"])
      .where("name", "=", data.coachName)
      .executeTakeFirst();

    if (!coach?.email) {
      console.log(`[USER_TO_COACH_NOTIFICATION] No email found for coach ${data.coachName}`);
      return { success: true, sentCount: 0, errors: [] };
    }

    // Use the existing getUserInfo utility to find the coach's user account
    const coachUser = await getUserInfo({ email: coach.email });

    if (!coachUser) {
      console.log(`[USER_TO_COACH_NOTIFICATION] Coach ${data.coachName} (${coach.email}) does not have a user account for push notifications`);
      return { success: true, sentCount: 0, errors: [] };
    }

    // Get all active push tokens for the coach's user account
    const tokens = await getActivePushTokens(coachUser.channelId);

    if (tokens.length === 0) {
      console.log(`[USER_TO_COACH_NOTIFICATION] No active push tokens found for coach ${data.coachName} (channel ${coachUser.channelId})`);
      return { success: true, sentCount: 0, errors: [] };
    }

    console.log(`[USER_TO_COACH_NOTIFICATION] Found ${tokens.length} active tokens for coach ${data.coachName} (channel ${coachUser.channelId})`);

    // Get user name for the notification
    let userName = data.userName;
    if (!userName) {
      const user = await db
        .selectFrom("user")
        .select(["name"])
        .where("channelId", "=", Number(data.userChannelId))
        .executeTakeFirst();
      userName = user?.name || `User ${data.userChannelId}`;
    }

    // Prepare notification payload
    const title = `DM - ${userName}`;
    const body = data.messageContent.length > 100
      ? `${data.messageContent.substring(0, 97)}...`
      : data.messageContent;

    const notificationData = {
      type: 'user_to_coach_message',
      userChannelId: data.userChannelId.toString(),
      coachName: data.coachName,
      messageId: data.messageId,
      userName: userName,
    };

    // Send notifications to all tokens
    const results = await Promise.allSettled(
      tokens.map(token =>
        sendExpoPushNotification({
          to: token.token,
          title,
          body,
          data: notificationData,
          sound: 'default',
          priority: 'high',
          channelId: 'user-messages',
        })
      )
    );

    // Process results
    let sentCount = 0;
    const errors: string[] = [];

    results.forEach((result, index) => {
      if (result.status === 'fulfilled' && result.value.success) {
        sentCount++;
      } else {
        const error = result.status === 'rejected'
          ? result.reason
          : result.value.error;
        errors.push(`Token ${tokens[index].id}: ${error}`);
        console.error(`[USER_TO_COACH_NOTIFICATION] Failed to send to token ${tokens[index].id}:`, error);
      }
    });

    console.log(`[USER_TO_COACH_NOTIFICATION] Sent ${sentCount}/${tokens.length} notifications successfully to coach ${data.coachName}`);

    if (errors.length > 0) {
      console.error(`[USER_TO_COACH_NOTIFICATION] Errors:`, errors);
    }

    return {
      success: sentCount > 0 || tokens.length === 0,
      sentCount,
      errors
    };

  } catch (error) {
    console.error("[USER_TO_COACH_NOTIFICATION] Error sending user to coach message notification:", error);
    return {
      success: false,
      sentCount: 0,
      errors: [error.message]
    };
  }
};


