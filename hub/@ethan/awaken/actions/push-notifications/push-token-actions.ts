"use server";

import { db } from "../../lib/db.ts";
import { v4 as uuidv4 } from "npm:uuid";

export interface PushTokenData {
  id: string;
  channelId: number;
  token: string;
  tokenType: string;
  platform: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

/**
 * Register a push token for a user
 */
export const registerPushToken = async (
  channelId: string | number,
  token: string,
  tokenType: string = "expo",
  platform: string
): Promise<{ success: boolean; id?: string; error?: string }> => {
  try {
    console.log(`[PUSH_TOKEN] Registering token for channel ${channelId}`);
    
    const channelIdNumber = Number(channelId);
    if (isNaN(channelIdNumber)) {
      return { success: false, error: "Invalid channel ID" };
    }

    // Check if this token already exists for this user
    const existingToken = await db
      .selectFrom("push_tokens")
      .select(["id", "is_active"])
      .where("channel_id", "=", channelIdNumber)
      .where("token", "=", token)
      .executeTakeFirst();

    if (existingToken) {
      // If token exists but is inactive, reactivate it
      if (!existingToken.is_active) {
        await db
          .updateTable("push_tokens")
          .set({
            is_active: true,
            updated_at: new Date().toISOString()
          })
          .where("id", "=", existingToken.id)
          .execute();
        
        console.log(`[PUSH_TOKEN] Reactivated existing token ${existingToken.id}`);
        return { success: true, id: existingToken.id };
      } else {
        console.log(`[PUSH_TOKEN] Token already exists and is active`);
        return { success: true, id: existingToken.id };
      }
    }

    // Deactivate any existing tokens for this user/platform combination
    await db
      .updateTable("push_tokens")
      .set({
        is_active: false,
        updated_at: new Date().toISOString()
      })
      .where("channel_id", "=", channelIdNumber)
      .where("platform", "=", platform)
      .execute();

    // Create new token record
    const tokenId = uuidv4();
    const now = new Date().toISOString();

    await db
      .insertInto("push_tokens")
      .values({
        id: tokenId,
        channel_id: channelIdNumber,
        token,
        token_type: tokenType,
        platform,
        is_active: true,
        created_at: now,
        updated_at: now
      })
      .execute();

    console.log(`[PUSH_TOKEN] Successfully registered new token ${tokenId} for channel ${channelId}`);
    return { success: true, id: tokenId };

  } catch (error) {
    console.error("[PUSH_TOKEN] Error registering push token:", error);
    return { success: false, error: error.message };
  }
};

/**
 * Get active push tokens for a user
 */
export const getActivePushTokens = async (channelId: string | number): Promise<PushTokenData[]> => {
  try {
    const channelIdNumber = Number(channelId);
    if (isNaN(channelIdNumber)) {
      return [];
    }

    const tokens = await db
      .selectFrom("push_tokens")
      .select([
        "id",
        "channel_id as channelId",
        "token",
        "token_type as tokenType",
        "platform",
        "is_active as isActive",
        "created_at as createdAt",
        "updated_at as updatedAt"
      ])
      .where("channel_id", "=", channelIdNumber)
      .where("is_active", "=", true)
      .execute();

    return tokens;
  } catch (error) {
    console.error("[PUSH_TOKEN] Error getting active push tokens:", error);
    return [];
  }
};

/**
 * Deactivate push tokens for a user (e.g., on logout)
 */
export const deactivatePushTokens = async (
  channelId: string | number,
  token?: string
): Promise<{ success: boolean; error?: string }> => {
  try {
    const channelIdNumber = Number(channelId);
    if (isNaN(channelIdNumber)) {
      return { success: false, error: "Invalid channel ID" };
    }

    let query = db
      .updateTable("push_tokens")
      .set({
        is_active: false,
        updated_at: new Date().toISOString()
      })
      .where("channel_id", "=", channelIdNumber);

    // If specific token provided, only deactivate that one
    if (token) {
      query = query.where("token", "=", token);
    }

    await query.execute();

    console.log(`[PUSH_TOKEN] Deactivated tokens for channel ${channelId}${token ? ` (token: ${token})` : ''}`);
    return { success: true };

  } catch (error) {
    console.error("[PUSH_TOKEN] Error deactivating push tokens:", error);
    return { success: false, error: error.message };
  }
};
