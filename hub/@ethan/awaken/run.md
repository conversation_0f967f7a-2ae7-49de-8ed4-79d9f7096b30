turso dev --db-file zero.db

deno task db:migrate @reframe/zero --url http://127.0.0.1:8080

turso dev --db-file awaken.db --port 8100

deno task db:migrate @ethan/awaken --url http://127.0.0.1:8100

deno task watch @ethan/awaken/main.tsx

ngrok http http://localhost:8000   

deno task clean

cat ../../../../hub/@ethan/awaken/.env.prod| fly secrets import

deno task db:migrate @ethan/awaken --prod

deno task build @ethan/awaken/main.tsx
deno task deploy @ethan/awaken   






mkdir .build/ethan
mkdir .build/ethan/hub

# copy fly.toml and Dockerfile (attached in thread) into .build/ethan

cp deno.json .build/ethan
cp -r hub/@ethan hub/@reframe .build/ethan/hub

# cd into build directory
cd .build/ethan

# make sure .build/@ethan/awaken has .env
# and make sure DATABASE_URL points to a local db, like DATABASE_URL=file:../ethan-awaken.db
# also IMPORTANT, if you already have DATABASE_URL set with fly cli, unset with `fly secrets unset DATABASE_URL DATABASE_AUTH_TOKEN`

deno add "npm:@libsql/kysely-libsql"
deno task db:migrate @ethan/awaken

# test app is running inside build directory -> might take a while to download everything from esm.sh

deno task watch @ethan/awaken/main.tsx

# make sure to add correct `app` name inside fly.toml, eg: `app = "ethan-awaken"` or `app = "awaken"` etc


fly deploy

fly logs # go to the production url in browser, keep an eye on logs and report in case of errors







# delete hub/ inside build directory
rm -r .build/ethan/hub
rm .build/ethan/.cache/commit/--@ethan--awaken--main.tsx.json

# copy latest files
mkdir .build/ethan/hub
cp -r hub/@ethan hub/@reframe .build/ethan/hub

# cd into the .build/ethan directory
cd .build/ethan

# run migration
deno task db:migrate @ethan/awaken

# verify
deno task watch @ethan/awaken/main.tsx

fly deploy

fly logs # go to the production url in browser and keep an eye on logs