import { MESSAGE_TYPES, UI_DEFAULT_TYPES, TEXTUAL_MSG_TYPES } from "./lib/message-types.ts";
import Reframe from "@";
import { Suspense, useState, useRef, useEffect } from "npm:react@canary";
import {
  Page,
  Y,
  X,
  Text,
  Image,
  For,
  Show,
  Redirect,
  Button,
} from "@reframe/ui/main.tsx";
import { createClient } from "./db/client.ts";
import { createRoute } from "@reframe/react/router.tsx";
import { render, reply, Render } from "@reframe/react/server.tsx";
import { assets } from "@reframe/tailwind/assets.ts";
import { Shell } from "@reframe/react/shell.tsx";
import { Async } from "@reframe/react/async.tsx";
import { Auth } from "./lib/auth.ts";
import { getSession } from "./lib/get-session.ts";
import { SignInComponent } from "./lib/sign-in.tsx";
import { getUserInfo } from "./lib/get-user-info.ts";
import { getAuth } from "@reframe/auth/auth.ts";
import { decryptProfile, getProfileText, getAllMessages, processTranscriptAction, sendDailyAwakeningEmail, getCoachPrompt, encryptProfile, saveMetafeedback } from "./action.ts";
import { getAuthenticatedUser } from "./lib/auth-helper.ts";

import { UnreadIcon } from "@reframe/icons/unread.ts";
import React from "npm:react@canary";
import { Welcome } from "./welcome.tsx";
import { Onboard } from "./routes/onboarding/onboarding.tsx";
import { Chat } from "./routes/chat/chat.tsx";
import { getFirstMessage, generateAudio, decryptMessages, initiateSubscription } from "./action.ts";

import { v4 as uuid } from "npm:uuid";
import { createUserInMake } from "./lib/upsert-user.ts";

import CryptoJS from "npm:crypto-js";
import stripe from "./lib/stripe-lib.ts";
import { Starred } from "./routes/starred/starred.tsx";
import { RefreshOverlay } from "./lib/refresh-overlay.tsx";
import { getAppVersion } from "./action.ts";
import { VersionCheck } from "./lib/version-check.tsx";
import { Changelog } from "./routes/changelog/changelog.tsx";
import { AdminChatAnalysis } from "./routes/admin/analysis/analysis.tsx";

import { IntegrationPage } from "./journeys.tsx";

import { db } from "./lib/db.ts";
import { getUserData } from "./lib/db.ts";
import { SUPERUSER_EMAILS, vapiAssistantConfigUrl, vapiAssistantFirstCallConfigUrl, vapiAssistantGoalSingleConfigUrl, soulListenerSystemPrompt } from "./lib/server-constants.ts";
import { PlanType, PLANS } from "./lib/plans.ts";
import { Terms } from "./routes/terms/terms.tsx";
import { PrivacyPolicy } from "./routes/privacy-policy/privacy-policy.tsx";

import { 
  saveMessage, 
  getMessages, 
  getStarredMessages, 
  updateMessageStatus,
  prepMessageHistoryAction
} from "./actions/db/conversation-actions.ts";

import { buildGoalCallAssistantConfig, handleCallEnd, setSummaryInfo } from "./actions/ai/call-actions.ts";
import { sql } from "npm:kysely";
import { handleVapiToolCall } from "./actions/ai/tool-handler.ts";
import { getUserCoachesAction, getCoachNameFromSlugAction } from "./actions/db/coach-actions.ts";
import { ConnectPage } from "./routes/connect/connect.tsx";
import { RedeemPage } from "./routes/redeem/redeem.tsx";

import { shouldSendAwakeningOnSchedule } from "./lib/awakening-helper.ts";
import { NotFoundPage } from "./404.tsx";
import { processOrganicEmails } from "./lib/organic-email.ts";
import { processDailyDigests } from "./lib/daily-digest-processor.ts"; 
import { processCoachDailyDigests } from "./lib/coach-digest-processor.ts";
import { processLimitlessTest } from "./lib/limitless-test-processor.ts";

import { AdminChatPage } from "./routes/admin/chat/page.tsx";
import { AdminDashboardPage } from "./routes/admin/dashboard/page.tsx";
import { getCoachByEmail } from "./routes/admin/actions/admin-actions.ts";
import { handleWebhookEvent, createGiftSession } from "./actions/stripe-actions.ts";
import { applyGift } from "./actions/db/gift-actions.ts";

// random push

export const App = createRoute<{ request: Request }>((Router) => (
  <Router.Route
    render={async (element, request) => {
      return new Response(
        render(
          <Shell assets={await assets()}>
              <style>
              {`html,body { background-color: #000; color: #fff; margin:0; padding:0; }`}
              </style>
              <title>Awaken</title>
              {/* Added global font preload to avoid layout shift on first paint */}
              <link rel="preconnect" href="https://fonts.googleapis.com" />
              <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
              <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Rethink+Sans:ital,wght@0,400..800;1,400..800&display=swap" />
              <link rel="icon" href="https://ik.imagekit.io/1f1ip9k7e/image.png?updatedAt=1738316508475" />
              <link rel="apple-touch-icon" href="https://ik.imagekit.io/1f1ip9k7e/awaken-apple-touch.png?updatedAt=1738578073179"/>
              <Render root="awaken-root">
                <Suspense
                  fallback={
                    <Y
                      justify={"center"}
                      align="center"
                      css={"bg-black h-screen w-screen"}
                    >
                      <UnreadIcon css="text-2xl text-white w-10 h-10 animate-spin" />
                      <Text css="text-gray-400 pt-3">Loading</Text>
                    </Y>
                  }
                >
                  <VersionCheck />
                  {element}
                </Suspense>
              </Render>
          </Shell>
        ),
        {
          headers: {
            "content-type": "text/html",
            "cache-control": "no-transform",
          },
        }
      );
    }}
    middleware={[
      Auth.middleware,
      async (request, next) => {
        const url = new URL(request.url);

        if (url.pathname.endsWith(".ico")) {
          console.log("returning 404 for favicon");
          return new Response(null, { status: 404 });
        }

        if (request.headers.get("x-react-server-action")) {
          return reply(request);
        }

        const response = await next(request);

        if (request.headers.get("upgrade") === "websocket") {
          return response;
        }

        try {
          response.headers.set("access-control-allow-origin", "*");
          response.headers.set(
            "access-control-allow-methods",
            "GET, POST, PUT, DELETE, PATCH, OPTIONS"
          );
          response.headers.set("access-control-allow-headers", "*");
        } catch (error) {
          console.error(
            "error setting cors headers",
            error.message,
            request.url
          );
        }

        // Add version endpoint
        if (url.pathname === '/api/version') {
          return Response.json(await getAppVersion());
        }



        return response;
      },
    ]}
    layout={(Router) => (
      <Auth.Provider>
        <Router.Outlet />
      </Auth.Provider>
    )}
    context={async () => {
      try {
        const token = self.localStorage.getItem("awaken-session") || undefined;

        console.log("token", token);

        const auth = await getSession(token);
        return auth;
      } catch {
        return { id: "", image: null, email: null, name: null };
      }
    }}
    
    page={() => (
      <Auth
        use={(auth) => (
          <Show
            when={auth.authenticated}
            then={() => <Redirect to="/chat" />}
            else={() => <Welcome />}
          />
        )}
      />
    )}

    route:auth={(Router) => (
      <Router.Route
        route:sign-in={(Router) => (
          <Router.Route
            layout={(Router) => (
              <Page>
                <Router.Outlet />
              </Page>
            )}
            page={(Router) => {
              const coachSlugFromQuery = Router.search?.coachSlug as string | undefined;
              const dataSharingEnabledFromQuery = Router.search?.dataSharingEnabled as string | undefined;

              return (
                <Auth
                  use={(auth) => (
                    <Show
                      when={!auth.authenticated}
                      then={() => (
                        <>
                          <SignInComponent />
                        </>
                      )}
                      else={() => {
                        return (
                          <Async
                            value={getAuthenticatedUser}
                            then={(e) => {
                              if (coachSlugFromQuery) {
                                const params = new URLSearchParams();
                                params.set('auto', '1');
                                if (dataSharingEnabledFromQuery) {
                                  params.set('dataSharingEnabled', dataSharingEnabledFromQuery);
                                }
                                return <Redirect to={`/connect/${coachSlugFromQuery}?${params.toString()}`} />;
                              }

                              if (e.onboarding === 1) {
                                return <Redirect to="/chat" />;
                              } else {
                                return <Redirect to="/onboarding" />;
                              }
                            }}
                          />
                        );
                      }}
                    />
                  )}
                />
              );
            }}
          />
        )}
        route:sign-out={(Route) => <Route.Route serve={() => Auth.signOut()} />}
        route:anonymous={(Router) => (
          <Router.Route
            serve={async (request) => {
              const appId = "awaken";

              const response = await Auth.signIn("credentials", { appId });
              console.log("response", response);
              const token = response.headers.get("x-session-token");

              if (!token) {
                console.error(
                  `missing x-session-token header for request with appId: ${appId}`
                );

                return new Response(
                  JSON.stringify({ error: "internal auth error" }),
                  {
                    status: 500,
                    headers: { "content-type": "application/json" },
                  }
                );
              }

              const auth = await getAuth(token);

              if (!auth.authenticated) {
                console.error(`failed to authenticate with token: ${token}`);

                return new Response(
                  JSON.stringify({ error: "internal auth error" }),
                  {
                    status: 500,
                    headers: { "content-type": "application/json" },
                  }
                );
              }

              const sessionId = auth.session.id;

              return new Response(
                JSON.stringify(
                  {
                    sessionId,
                    token,
                  },
                  null,
                  2
                ),
                { headers: { "content-type": "application/json" } }
              );
            }}
          />
        )}
      />
    )}

    route:chat={(Router) => (
      <Router.Route
        page={() => {
          return (
            <Async
              value={getAuthenticatedUser}
              then={async (user) => {
                if (!user) {
                  return <Redirect to="/auth/sign-out" />;
                }

                if (user.onboarding === 0) {
                  return <Onboard user={user} />;
                }

                // const messages = await getAllMessages(user.channelId, 25);
                // console.log("messages", messages);
                const userData = await getUserData(user.channelId);
                let coaches;
                try {
                  coaches = await getUserCoachesAction(user.channelId.toString());
                } catch (error) {
                  console.error("[COACH_FETCH] Error fetching available coaches:", error);
                  coaches = ["Kokoro"]; // Fallback on error
                }
                const messages = [];
                for (const coach of coaches) {
                  const coachMessages = await getAllMessages(user.channelId, 25, undefined, coach, UI_DEFAULT_TYPES);
                  messages.push(...coachMessages);
                }
                console.log("MESSAGES MESSAGES MESSAGES", messages.length);
                return <Chat user={user} messages={messages} enrollmentMessage={null} initialUserData={userData} availableCoaches={coaches} />;
              }}
              catch={(error) => {
                console.error(error);
                console.log("ERRORRR");
                return <Redirect to="/auth/sign-in" />;
              }}
            />
          );
        }}
      />
    )}

    route:onboarding={(Router) => (
      <Router.Route
        page={() => {
          return (
            <Async
              value={getAuthenticatedUser}
              then={(user) => {
                if (user.onboarding === 1) {
                  return <Redirect to="/chat" />;
                }
                return <Onboard user={user} />;
              }}
              catch={(error) => {
                console.error(error);
                console.log("ERRORRR");
                return <Redirect to="/auth/sign-in" />;
              }}
            />
          );
        }}
      />
    )}

    route:admin-chat={Router => (
      <Router.Route
        page={async () => {
          const user = await getSession();
          if (!user) {
            return <Redirect to="/sign-in" />;
          }
          
          // Check if user is superuser or coach
          const isSuperUser = SUPERUSER_EMAILS.includes(user.email);
          let isCoach = false;
          
          if (!isSuperUser) {
            const coach = await getCoachByEmail(user.email);
            isCoach = !!coach;
          }
          
          if (!isSuperUser && !isCoach) {
            return <Redirect to="/chat" />;
          }
          
          return <AdminChatPage />;
        }}
      />
    )}

    route:integration={(Router) => (
      <Router.Route
        page={() => {
          return (
            <Async
              value={async () => {
                const user = await getAuthenticatedUser();
                if (!user) return null;
                
                // 1. Fetch the user's awakening from the 'Awakenings' table
                const awakeningResp = await fetch(
                  `https://api.airtable.com/v0/${Reframe.env.AIRTABLE_BASE_ID}/awakenings?filterByFormula=AND({user_id}='${user.channelId}')&maxRecords=1&sort%5B0%5D%5Bfield%5D=date&sort%5B0%5D%5Bdirection%5D=desc`,
                  {
                    headers: { Authorization: `Bearer ${Reframe.env.AIRTABLE_API_KEY}` },
                  }
                );
                const awakeningData = await awakeningResp.json();
                let awakeningRec = awakeningData.records?.[0];

                // 2. If an awakening is found, fetch practice, commitment, notes by referencing the awakening's record ID
                let practiceRec = null;
                let commitmentRec = null;
                let notesRec = null;
                if (awakeningRec) {
                  const awakeningId = awakeningRec.fields.awakening_id;
                  const practiceResp = await fetch(
                    `https://api.airtable.com/v0/${Reframe.env.AIRTABLE_BASE_ID}/practices?filterByFormula=AND({awakening_id}='${awakeningId}')&maxRecords=1&sort%5B0%5D%5Bfield%5D=date&sort%5B0%5D%5Bdirection%5D=desc`,
                    { headers: { Authorization: `Bearer ${Reframe.env.AIRTABLE_API_KEY}` } }
                  );
                  const practiceData = await practiceResp.json();
                  practiceRec = practiceData.records?.[0] || null;

                  const commitmentResp = await fetch(
                    `https://api.airtable.com/v0/${Reframe.env.AIRTABLE_BASE_ID}/commitments?filterByFormula=AND({awakening_id}='${awakeningId}')&maxRecords=1&sort%5B0%5D%5Bfield%5D=date&sort%5B0%5D%5Bdirection%5D=desc`,
                    { headers: { Authorization: `Bearer ${Reframe.env.AIRTABLE_API_KEY}` } }
                  );
                  const commitmentData = await commitmentResp.json();
                  commitmentRec = commitmentData.records?.[0] || null;

                  const notesResp = await fetch(
                    `https://api.airtable.com/v0/${Reframe.env.AIRTABLE_BASE_ID}/awakening_notes?filterByFormula=AND({awakening_id}='${awakeningId}')&maxRecords=1&sort%5B0%5D%5Bfield%5D=date&sort%5B0%5D%5Bdirection%5D=desc`,
                    { headers: { Authorization: `Bearer ${Reframe.env.AIRTABLE_API_KEY}` } }
                  );
                  const notesData = await notesResp.json();
                  notesRec = notesData.records?.[0] || null;
                }

                /* 3. Fetch some recent Kokoro messages for user
                const msgResp = await fetch(
                  `https://api.airtable.com/v0/${Reframe.env.AIRTABLE_BASE_ID}/Kokoro n8n Convo Store Dev?filterByFormula=AND({Channel}='${user.channelId}', {Sender}='assistant')&maxRecords=5&sort%5B0%5D%5Bfield%5D=Date&sort%5B0%5D%5Bdirection%5D=desc`,
                  {
                    headers: { Authorization: `Bearer ${Reframe.env.AIRTABLE_API_KEY}` },
                  }
                );
                const msgData = await msgResp.json();
                */

                // 4. Prepare the data for the page
                const awakeningContent = awakeningRec?.fields?.content || "";
                const practiceContent = practiceRec?.fields?.content || "";
                const commitmentContent = commitmentRec?.fields?.content || "";
                const commitmentDate = commitmentRec?.fields?.date || "";
                const notesContent = notesRec?.fields?.content || "";
                /* const messages = msgData.records?.map((r) => ({
                  Id: r.id,
                  Content: r.fields.Content || "",
                  Date: r.fields.Date || "",
                })) || [];
                */

                return {
                  user,
                  awakening: awakeningRec ? { id: awakeningRec.id, content: awakeningContent } : null,
                  practice: practiceRec ? { id: practiceRec.id, content: practiceContent } : null,
                  commitment: commitmentRec ? { id: commitmentRec.id, content: commitmentContent, date: commitmentDate } : null,
                  notes: notesRec ? { id: notesRec.id, content: notesContent } : null
                };
              }}
              then={({ user, awakening, practice, commitment, notes }) => {
                if (!user) {
                  return <Redirect to="/auth/sign-out" />;
                }
                return (
                  <IntegrationPage
                    user={user}
                    awakening={awakening}
                    practice={practice}
                    commitment={commitment}
                    notes={notes}
                  />
                );
              }}
              catch={(error) => {
                console.error(error);
                return <Redirect to="/auth/sign-in" />;
              }}
            />
          );
        }}
      />
    )}

    route:changelog={Router => (
      <Router.Route
        page={async () => {
          console.log('[App] Initializing changelog route');
          try {
            const auth = await getAuth();
            console.log('[App] Auth result:', auth);
            
            if (!auth?.authenticated || !auth?.session?.email) {
              console.log('[App] User not authenticated or missing email');
              throw new Error("unauthorized");
            }

            const user = await getUserInfo({
              email: auth.session.email,
            });

            console.log('[App] User info:', user);

            if (!user?.email) {
              console.log('[App] No valid user info found');
              return null;
            }

            console.log('[App] Rendering changelog for user:', user.email);
            return <Changelog user={user} />;
          } catch (error) {
            console.error('[App] Changelog route error:', error);
            throw error;
          }
        }}
      />
    )}

    // Add terms and privacy policy routes at the root level, similar to other routes
    route:terms={(Router) => (
      <Router.Route
        page={({ url }) => {
          return <Terms />;
        }}
      />
    )}
    
    route:privacy-policy={(Router) => (
      <Router.Route
        page={({ url }) => {
          return <PrivacyPolicy />;
        }}
      />
    )}

    route:health={(Router) => (
      <Router.Route
        serve:get={async () => {
          try {
            console.log("[HEALTH] Starting comprehensive health check...");

            // Import and run the comprehensive heat-up function
            const { heatUpServerActions } = await import("./lib/heat-up.ts");
            await heatUpServerActions();

            console.log("[HEALTH] Health check completed successfully");
            return new Response("OK - All server actions heated up and ready", { status: 200 });
          } catch (error) {
            console.error("[HEALTH] Health check failed:", error);
            return new Response(`Health check failed: ${error.message}`, { status: 500 });
          }
        }}
      />
    )}

    route:not-found={(Router) => (
      <Router.Route
        page={() => <NotFoundPage />}
      />
    )}

    route:admin={Router => (
      <Router.Route
        page={() => <AdminDashboardPage />}
        route:analysis={Router2 => (
          <Router2.Route
            page={async () => {
              const user = await getSession();
              if (!user) {
                return <Redirect to="/sign-in" />;
              }
              // Only superusers can access analysis
              if (!SUPERUSER_EMAILS.includes(user.email)) {
                return <Redirect to="/chat" />;
              }
              return <AdminChatAnalysis user={user} />;
            }}
          />
        )}
        route:chat={Router2 => (
          <Router2.Route
            page={async () => {
              const user = await getSession();
              if (!user) {
                return <Redirect to="/sign-in" />;
              }
              
              // Check if user is superuser or coach
              const isSuperUser = SUPERUSER_EMAILS.includes(user.email);
              let isCoach = false;
              
              if (!isSuperUser) {
                const coach = await getCoachByEmail(user.email);
                isCoach = !!coach;
              }
              
              if (!isSuperUser && !isCoach) {
                return <Redirect to="/chat" />;
              }
              
              return <AdminChatPage />;
            }}
          />
        )}
      />
    )}
    
    route:redeem={(Router) => (
      <Router.Route
        page={() => {
          return (
            <Async
              value={getAuthenticatedUser}
              then={(user) => <RedeemPage user={user} />}
              catch={(error) => {
                console.error("Auth error in redeem:", error);
                return <RedeemPage />;
              }}
            />
          );
        }}
      />
    )}
    
    route:starred={(Router) => (
      <Router.Route
        page={() => {
          return (
            <Async
              value={async () => {
                const user = await getAuthenticatedUser();
                if (!user) return null;
                console.log("[STARRED] Fetching starred messages for user:", user.channelId);
                
                const starred = await getStarredMessages(user.channelId);
                console.log(`[STARRED] Retrieved ${starred.length} starred messages`);
                
                return { user, starred };
              }}
              then={({ user, starred }) => {
                if (!user) return <Redirect to="/auth/sign-out" />;
                // Render the new Starred component, passing starred messages
                return <Starred user={user} starred={starred} />;
              }}
              catch={(error) => {
                console.error("[STARRED] Error:", error);
                return <Redirect to="/auth/sign-in" />;
              }}
            />
          );
        }}
      />
    )}

    route:notfound={(Router) => (
      <Router.Route
        page={() => {
          console.log("NOT FOUND");
          return <NotFoundPage />;
        }}
      />
    )}

    route:test={(Router) => (
      <Router.Route
        route={(id) => (Router) => (
          <Router.Route
            page={() => {
              return <X> TESTING test {id} </X>;
            }}
          />
        )}
      />
    )}

    route:connect={(Router) => (
      <Router.Route
        route={(coachSlug) => (Router) => (
          <Router.Route
            page={() => {
              return (
                <Async
                  value={async () => {
                    // Allow unauthenticated users to view the connect page. If
                    // the user is logged-in we still fetch their userData,
                    // otherwise we just return nulls so the page can render a
                    // sign-in prompt.
                    let user = null;
                    try {
                      user = await getAuthenticatedUser();
                    } catch (error) {
                      console.error("Error fetching user:", error);
                    }

                    let userData = null;
                    if (user) {
                      userData = await getUserData(user.channelId);
                    }

                    return { user, userData, coachSlug };
                  }}
                  then={({ user, userData, coachSlug }) => (
                    <ConnectPage
                      // Pass an empty object when unauthenticated so the Layout
                      // component still receives a user prop.
                      user={user ?? {}}
                      userData={userData}
                      enrollmentMessage={null}
                      isLoadingEnrollment={false}
                      coachSlug={coachSlug}
                    />
                  )}
                  catch={(error) => {
                    console.error("Connect route error:", error);
                    return <Redirect to="/auth/sign-inn" />;
                  }}
                />
              );
            }}
          />
        )}
      />
    )}
    
    route:encrypt-call={(Router) => (
      <Router.Route
        serve:post={async (request, context) => {
          const requestBody = await request.json();
          const { records } = requestBody;

          const secretKey = Reframe.env.SECRET_KEY;

          // Encrypt only Content field, preserve other fields
          const encryptedMessages = records.map((record) => {
            return {
              fields: {
                ...record.fields,
                Content: CryptoJS.AES.encrypt(
                  record.fields.Content,
                  secretKey
                ).toString(),
              },
            };
          });

          return Response.json({
            encryptedMessages,
          });
        }}
      />
    )}

    route:decrypt={(Router) => (
      <Router.Route
        serve:post={async (request, context) => {
          try {
            const requestBody = await request.json();
            const { channelId, parse } = requestBody;
            
            console.log("BOY WE'RE HERE DECRYPTING");

            const result = await decryptMessages(channelId, parse);
            return Response.json(result);

          } catch (error) {
            console.error("[DECRYPT] Error in decrypt route:", {
              name: error.name,
              message: error.message,
              stack: error.stack
            });
            
            return Response.json({
              error: error.message,
              success: false
            }, { status: 500 });
          }
        }}
      />
    )}
    
    route:save-metafeedback={(Router) => (
      <Router.Route
        serve:post={async (request, context) => {
          const requestBody = await request.json();
          const { channelId, metafeedback } = requestBody;

          await saveMetafeedback(channelId, metafeedback);

          return Response.json({ success: true });
        }}
      />
    )}
    
    // ADDED a new route 'integration' that fetches awakening, practice, commitment, notes, and recent Kokoro messages, then renders IntegrationPage

    // route that returns the entire env as a json object
    // route:getenv={Router => (
    //   <Router.Route
    //     serve:get={async () => {
    //       return Response.json(Reframe.env);
    //     }}
    //   />
    // )}

    route:handle-call-end={(Router) => (
      <Router.Route
        serve:post={async (request) => {
          try {
            const body = await request.json();
            // console.log("handle-call-end body", body);
            const { message } = body;
            await handleCallEnd(message);
            return new Response("Completed", { status: 200 });
          } catch (error) {
            console.error("Error in /handle-call-end:", error);
            return new Response(error.message, { status: 500 });
          }
        }}
      />
    )}

    route:api={Router => (
      <Router.Route
        route:create-checkout-session={Router => (
          <Router.Route
            serve={async (request) => {
              console.log('[API] Received checkout session request');
              
              try {
                if (request.method !== 'POST') {
                  console.error('[API] Invalid method:', request.method);
                  return new Response('Method not allowed', { 
                    status: 405,
                    headers: { 'Content-Type': 'text/plain' }
                  });
                }

                console.log('[API] Calling initiateSubscription');
                const session = await initiateSubscription(request);
                return session;

              } catch (error) {
                console.error('[API] Checkout session error:', {
                  name: error.name,
                  message: error.message,
                  stack: error.stack
                });
                
                return new Response(
                  error instanceof Error ? error.message : 'Internal server error',
                  { 
                    status: 500,
                    headers: { 'Content-Type': 'text/plain' }
                  }
                );
              }
            }}
          />
        )}
        route:tool-handler={(Router) => (
          <Router.Route
            method="POST"
            serve={handleVapiToolCall}
          />
        )}
        route:gift={(Router) => (
          <Router.Route
            route:session={(Router) => (
              <Router.Route
                serve={async (request) => {
                  if (request.method !== 'POST') {
                    return new Response('Method not allowed', { status: 405 });
                  }

                  try {
                    const user = await getAuthenticatedUser();
                    if (!user) {
                      return new Response('Unauthorized', { status: 401 });
                    }

                    const body = await request.json();
                    const { amount, recipientEmail, essenceAmount, message, senderName } = body;

                    if (!amount || !recipientEmail || !essenceAmount) {
                      return new Response('Missing required fields', { status: 400 });
                    }

                    const checkoutUrl = await createGiftSession(
                      user.channelId.toString(),
                      recipientEmail,
                      essenceAmount,
                      amount,
                      message,
                      senderName || user.name
                    );

                    return Response.json({ checkoutUrl });
                  } catch (error) {
                    console.error('[API] Gift session error:', error);
                    return new Response(
                      error instanceof Error ? error.message : 'Internal server error',
                      { status: 500 }
                    );
                  }
                }}
              />
            )}
          />
        )}
        route:redeem={(Router) => (
          <Router.Route
            serve={async (request) => {
              if (request.method !== 'POST') {
                return new Response('Method not allowed', { status: 405 });
              }

              try {
                const user = await getAuthenticatedUser();
                if (!user) {
                  return new Response('Unauthorized', { status: 401 });
                }

                const body = await request.json();
                const { code } = body;

                if (!code) {
                  return new Response('Missing gift code', { status: 400 });
                }

                const result = await applyGift(code, user.channelId);

                if (!result.success) {
                  return new Response(result.error || 'Failed to redeem gift', { status: 400 });
                }

                return Response.json({
                  success: true,
                  balance: result.balance,
                  amount: result.amount,
                  message: result.message,
                  senderName: result.senderName
                });
              } catch (error) {
                console.error('[API] Redeem error:', error);
                return new Response(
                  error instanceof Error ? error.message : 'Internal server error',
                  { status: 500 }
                );
              }
            }}
          />
        )}
        route:debug-gift={(Router) => (
          <Router.Route
            serve={async (request) => {
              console.log("[DEBUG] Gift debug endpoint called");
              
              if (request.method !== 'POST') {
                return new Response('Method not allowed', { status: 405 });
              }

              try {
                const body = await request.json();
                const { code, essenceAmount, recipientEmail, senderName, message } = body;
                
                console.log("[DEBUG] Creating test gift with:", { code, essenceAmount, recipientEmail });
                
                // Import the function
                const { createGiftRow } = await import("./actions/db/gift-actions.ts");
                
                const gift = await createGiftRow({
                  code: code || "TESTCODE",
                  essenceAmount: essenceAmount || 100,
                  purchaserEmail: "<EMAIL>",
                  recipientEmail: recipientEmail || "<EMAIL>",
                  senderName: senderName || "Test Sender",
                  message: message || "Test gift",
                  stripePaymentIntentId: "pi_test_" + Date.now(),
                });
                
                return Response.json({
                  success: true,
                  gift
                });
              } catch (error) {
                console.error('[DEBUG] Gift creation error:', error);
                return Response.json({
                  success: false,
                  error: error.message,
                  stack: error.stack
                }, { status: 500 });
              }
            }}
          />
        )}
        route:push-tokens={Router => (
          <Router.Route
            route:register={Router => (
              <Router.Route
                serve:post={async (request) => {
                  try {
                    console.log('[API] Received push token registration request');

                    const body = await request.json();
                    const { channelId, token, type, platform } = body;

                    if (!channelId || !token || !type || !platform) {
                      console.error('[API] Missing required fields:', { channelId: !!channelId, token: !!token, type: !!type, platform: !!platform });
                      return new Response('Missing required fields', {
                        status: 400,
                        headers: { 'Content-Type': 'text/plain' }
                      });
                    }

                    console.log('[API] Registering push token:', { channelId, type, platform, tokenPreview: token.substring(0, 20) + '...' });

                    // Import the push token registration function
                    const { registerPushToken } = await import('./actions/push-notifications/push-token-actions.ts');
                    const result = await registerPushToken(channelId, token, type, platform);

                    console.log('[API] Push token registration result:', result);
                    return Response.json(result);
                  } catch (error) {
                    console.error('[API] Error registering push token:', error);
                    return new Response(
                      error instanceof Error ? error.message : 'Internal server error',
                      {
                        status: 500,
                        headers: { 'Content-Type': 'text/plain' }
                      }
                    );
                  }
                }}
              />
            )}
          />
        )}
        route:push-notifications={Router => (
          <Router.Route
            route:test={Router => (
              <Router.Route
                serve:post={async (request) => {
                  try {
                    console.log('[API] Received test push notification request');

                    const body = await request.json();
                    const { channelId, title, message } = body;

                    if (!channelId) {
                      console.error('[API] Missing channelId');
                      return new Response('Missing channelId', {
                        status: 400,
                        headers: { 'Content-Type': 'text/plain' }
                      });
                    }

                    console.log('[API] Sending test notification:', { channelId, title, message });

                    // Import the test notification function
                    const { sendTestNotification } = await import('./actions/push-notifications/push-notification-service.ts');
                    const result = await sendTestNotification(
                      channelId,
                      title || "Test Notification",
                      message || "This is a test notification from Awaken"
                    );

                    console.log('[API] Test notification result:', result);
                    return Response.json(result);
                  } catch (error) {
                    console.error('[API] Error sending test notification:', error);
                    return new Response(
                      error instanceof Error ? error.message : 'Internal server error',
                      {
                        status: 500,
                        headers: { 'Content-Type': 'text/plain' }
                      }
                    );
                  }
                }}
              />
            )}
          />
        )}
      />
    )}

    route:webhook={Router => (
      <Router.Route
        serve:post={async (request) => {
          console.log("[WEBHOOK] Received webhook request at /webhook");
          console.log("[WEBHOOK] Headers:", Object.fromEntries(request.headers.entries()));
          
          try {
            const signature = request.headers.get("stripe-signature");
            const body = await request.text();
            
            console.log("[WEBHOOK] Processing event...");
            console.log("[WEBHOOK] Signature present:", !!signature);
            console.log("[WEBHOOK] Body length:", body.length);
            console.log("[WEBHOOK] Webhook secret exists:", !!Reframe.env.STRIPE_WEBHOOK_SECRET);
            
            const event = await stripe.webhooks.constructEvent(
              body,
              signature,
              Reframe.env.STRIPE_WEBHOOK_SECRET
            );

            console.log("[WEBHOOK] Event type:", event.type);
            console.log("[WEBHOOK] Event ID:", event.id);
            
            // Log checkout session details if it's a checkout.session.completed event
            if (event.type === "checkout.session.completed") {
              console.log("[WEBHOOK] Session metadata:", event.data.object.metadata);
            }

            const result = await handleWebhookEvent(event);
            return result;
          } catch (error) {
            console.error("[WEBHOOK] Error:", error);
            console.error("[WEBHOOK] Error details:", error.message);
            return new Response(error.message, { status: 400 });
          }
        }}
      />
    )}

    // Catch-all route for coach slugs (e.g., /coach-name)
    route={(potentialCoachSlug) => (Router) => (
      <Router.Route
        page={() => {
          return (
            <Async
              value={async () => {
                // Check if this segment is a valid coach slug
                const coachName = await getCoachNameFromSlugAction(potentialCoachSlug);
                if (!coachName) {
                  // Not a coach slug, return null to fall through to 404
                  return null;
                }
                
                // console.log("JUST HERE");
                let user = null;
                try {
                  user = await getAuthenticatedUser();
                } catch (error) {
                  console.error("Error fetching user:", error);
                  // console.log("PASSING THROGUHT THIS");
                }
                if (!user) return { coachSlug: potentialCoachSlug, user: null, userData: null };
                
                let userData = null;
                if (user) {
                  userData = await getUserData(user.channelId);
                }
                
                return { user, userData, coachSlug: potentialCoachSlug };
              }}
              then={(data) => {
                if (!data) {
                  // Fall through to 404 if not a valid coach
                  return <NotFoundPage />;
                }
                
                const { user, userData, coachSlug } = data;
                
                return (
                  <ConnectPage
                    user={user}
                    userData={userData}
                    enrollmentMessage={null}
                    isLoadingEnrollment={false}
                    coachSlug={coachSlug}
                  />
                );
              }}
              catch={(error) => {
                console.error("Coach route error:", error);
                return <Redirect to="/auth/sign-in" />;
              }}
            />
          );
        }}
      />
    )}
  />
));

// Update the type definitions
type AirtablePlan = 'basic_month' | 'basic_year' | 'premium_month' | 'premium_year' | 'free_plan';

// Add plan mapping
const PLAN_MAPPING: Record<string, AirtablePlan> = {
  'basic_plan_monthly': 'basic_month',
  'basic_plan_yearly': 'basic_year',
  'premium_plan_monthly': 'premium_month',
  'premium_plan_yearly': 'premium_year'
};

const cron = (
  label: string,
  seconds: number,
  fn: () => Promise<void>,
) => {
  const current = {} as { promise?: Promise<void> };

  if (!(globalThis as any).cronIntervals) {
    (globalThis as any).cronIntervals = new Map();
  }
  
  const cronIntervals = (globalThis as any).cronIntervals;

  if (cronIntervals.has(label)) {
    clearInterval(cronIntervals.get(label))
  }

  const interval = setInterval(async () => {
    console.log(`[cron] [start] (${label})`);
    try {
      if (!current.promise) {
        current.promise = fn();
      }
      await current.promise;
      console.log(`[cron] [success] (${label})`);
    } catch (error) {
      console.log(`[cron] [error] (${label})`, (error as Error).stack);
    } finally {
      current.promise = undefined;
    }
  }, seconds * 1000);
  
  cronIntervals.set(label, interval);
};

// cron("sync", 5, async () => {
//   // wait 2.5 seconds
//   console.log("[sync-job-started]");
//   await new Promise((r) => setTimeout(r, 2500));
//   console.log("[sync-job-completed]");
// });


// Generate daily awakening messages for users where it's 6am in their timezone
const generateDailyAwakenings = async () => {
  // Skip daily awakening when organic emails are enabled
  if (Reframe.env.FEAT_ORGANIC_EMAIL === "on") {
    console.log("[CRON] Daily awakening disabled - organic emails active");
    return;
  }
  
  console.log("[CRON] Running daily awakening check");
  
  try {
    // Get the current UTC time
    const now = new Date();
    
    // Find all users with timezones set
    const allUsers = await db
      .selectFrom("user")
      .select(["channelId", "email", "timezone", "lastDailyAwakeningDate", "firstName", "emailDaily", "selectedCoach"])
      .where(eb => 
        eb.or([
          // Only include users who have a timezone set
          eb("timezone", "is not", null)
        ])
      )
      .execute();
    
    console.log(`[CRON] Found ${allUsers.length} users with timezones`);
    
    // Pre-filter users who are eligible for awakening messages (6am in their timezone and no message today)
    const eligibleUsers = allUsers.filter(user => {
      if (!user.timezone) return false;
      
      // Skip users who have opted out of daily emails
      if (user.emailDaily === 0) {
        console.log(`[CRON] User ${user.channelId} has opted out of daily emails. Skipping.`);
        return false;
      }
      
      // Get the current date and hour in user's timezone
      const userTime = new Date(now.toLocaleString("en-US", { timeZone: user.timezone }));
      const userHour = userTime.getHours();
      const userDateStr = userTime.toISOString().split('T')[0];
      
      // Log current user time data
      console.log(`[CRON] User ${user.channelId} has timezone ${user.timezone}, hour ${userHour}, date ${userDateStr}, last awakening date: ${user.lastDailyAwakeningDate || 'never'}`);
      
      // User is eligible if it's 6am AND they haven't received a message today yet
      return userHour === 6 && user.lastDailyAwakeningDate !== userDateStr;
    });
    
    console.log(`[CRON] Found ${eligibleUsers.length} users eligible for daily awakening messages`);
    
    if (eligibleUsers.length === 0) {
      console.log("[CRON] No eligible users found for daily awakening messages");
      return;
    }
    
    // Process eligible users in parallel
    const results = await Promise.all(
      eligibleUsers.map(async (user) => {
        try {
          console.log(`[CRON] Processing daily awakening for user ${user.channelId} in timezone ${user.timezone}`);
          
          // Get the current date in user's timezone for tracking
          const userTime = new Date(now.toLocaleString("en-US", { timeZone: user.timezone }));
          const userDateStr = userTime.toISOString().split('T')[0];
          
          // Get user profile and message history in parallel
          // OPTIMIZE TODO: get only last 4 messages
          const [profileData, messagesData] = await Promise.all([
            getProfileText(user.channelId.toString()),
            decryptMessages(user.channelId.toString(), true, "all", 25, TEXTUAL_MSG_TYPES)
          ]);
          
          // Add detailed logging to debug message structure
          console.log(`[CRON] User ${user.channelId} - Message data type:`, typeof messagesData);
          console.log(`[CRON] User ${user.channelId} - Is array:`, Array.isArray(messagesData));
          console.log(`[CRON] User ${user.channelId} - Message count:`, Array.isArray(messagesData) ? messagesData.length : 'N/A');
          
          if (Array.isArray(messagesData) && messagesData.length > 0) {
            console.log(`[CRON] User ${user.channelId} - First message sample:`, JSON.stringify(messagesData[0]));
            
            // Check message type property
            const messageTypeProperty = messagesData[0].Type !== undefined ? 'Type' : 
                                       messagesData[0].message_type !== undefined ? 'message_type' : 
                                       messagesData[0].MessageType !== undefined ? 'MessageType' : null;
            
            console.log(`[CRON] User ${user.channelId} - Message type property:`, messageTypeProperty);
          }
          
          // Check if user should receive awakening based on 1st, 3rd, 7th day schedule
          const awakeningCheck = shouldSendAwakeningOnSchedule(messagesData, user.channelId);
          
          if (!awakeningCheck.shouldSendAwakening) {
            console.log(`[CRON] User ${user.channelId} - ${awakeningCheck.reason}. Skipping.`);
            return {
              channelId: user.channelId,
              success: false,
              skipped: true,
              reason: awakeningCheck.reason,
              daysSinceLastInteraction: awakeningCheck.daysSinceLastInteraction
            };
          }
          
          console.log(`[CRON] User ${user.channelId} - ${awakeningCheck.reason}. Proceeding with awakening.`);
          
          // Decrypt profile
          const { profileText } = profileData;
          // Process message history using our new helper function
          console.log(`[CRON] Preparing message history for user ${user.channelId}`);
          const { conversationText, timeElapsedNote } = prepMessageHistoryAction(messagesData);
          console.log(`[CRON] Message history prepared with time elapsed: ${timeElapsedNote}`);
          
          // Get the user's selected coach from the database or fall back to Kokoro
          const userCoach = user.selectedCoach || "Kokoro";
          console.log(`[CRON] Using coach "${userCoach}" for user ${user.channelId}`);
          
          // Get the coach prompt from the database
          const coachPromptResponse = await getCoachPrompt(userCoach, "dailyAwakeningPrompt");
          const awakeningPrompt = coachPromptResponse.SystemPrompt;
          
          // Prepare prompt for Claude
          const userPrompt = `
            <CLIENT_PROFILE>${profileText}</CLIENT_PROFILE>
            <MESSAGE_HISTORY>${conversationText}</MESSAGE_HISTORY>
            <TIME_ELAPSED>${timeElapsedNote}</TIME_ELAPSED>
            `;
          
          // Create the system array with the text prompt
          const systemArray = [
            {
              "type": "text",
              "text": awakeningPrompt,
              "cache_control": { "type": "ephemeral" }
            }
          ];
          
          // Call Claude
          const messageArray = [
            {
              "role": "user",
              "content": userPrompt
            }
          ];
          const response = await fetch(`https://api.anthropic.com/v1/messages`,
              {
                method: "POST",
                headers: {
                  "Content-Type": "application/json",
                  "x-api-key": Reframe.env.CLAUDE_API_KEY,
                  "anthropic-version": "2023-06-01",
                  "anthropic-beta": "prompt-caching-2024-07-31",
                },
                body: JSON.stringify({
                  model: "claude-3-7-sonnet-20250219",
                  system: systemArray,
                  messages: messageArray,
                  max_tokens: 2000,
                  temperature: 1.0,
                  thinking: {
                    type: "enabled",
                    budget_tokens: 1024
                  }
                })
              }
          );
          
          if (!response.ok) {
            throw new Error(`Claude API error: ${response.status} ${await response.text()}`);
          }
          
          const claudeResponse = await response.json();
          
          // For THINKING, retrieve text response from data.content[1].text instead of data.content[0].text (0 is the thinking response)
          const awakeningMessage = claudeResponse.content[1].text;
          
          // Extract just the message content from the XML tags
          const messageMatch = awakeningMessage.match(/<AWAKENING_MESSAGE>([\s\S]*?)<\/AWAKENING_MESSAGE>/);
          let awakeningBody = "";
          if (messageMatch && messageMatch[1]) {
            awakeningBody = messageMatch[1].trim();
          }

          // Extract just the subject line from the XML tags
          const subjectMatch = awakeningMessage.match(/<SUBJECT>([\s\S]*?)<\/SUBJECT>/);
          let awakeningSubjectLine = "";
          if (subjectMatch && subjectMatch[1]) {
            awakeningSubjectLine = subjectMatch[1].trim();
          }
          // Perform message saving and email sending in parallel
          await Promise.all([
            // 1. Save the daily awakening message to the database
            saveMessage(
              user.channelId, 
              "assistant", 
              awakeningBody, 
              undefined, 
              "Default", 
              "daily_awakening"
            ),
            
            // 2. Send email if user has an email address and has not opted out of daily emails
            (user.email && user.emailDaily !== 0) ? 
              sendDailyAwakeningEmail(user.email, awakeningSubjectLine, awakeningBody, user.firstName) : 
              Promise.resolve()
          ]);
          
          // Update the last daily awakening date for this user
          await db
            .updateTable("user")
            .set({ lastDailyAwakeningDate: userDateStr })
            .where("channelId", "=", user.channelId)
            .execute();
          
          console.log(`[CRON] Successfully generated daily awakening for user ${user.channelId} and updated last date to ${userDateStr}`);
          
          return {
            channelId: user.channelId,
            success: true,
            emailSent: !!(user.email && user.emailDaily !== 0),
            emailSkipped: !!(user.email && user.emailDaily === 0)
          };
        } catch (error) {
          console.error(`[CRON] Error generating daily awakening for user ${user.channelId}:`, error);
          return {
            channelId: user.channelId,
            success: false,
            error: error.message || "Unknown error"
          };
        }
      })
    );
    
    // Log summary of results
    const successCount = results.filter(r => r.success).length;
    const emailCount = results.filter(r => r.emailSent).length;
    const skippedCount = results.filter(r => r.skipped).length;
    const emailSkippedCount = results.filter(r => r.emailSkipped).length;
    console.log(`[CRON] Daily awakening summary: ${successCount}/${results.length} successful, ${emailCount} emails sent, ${skippedCount} skipped due to awakening schedule, ${emailSkippedCount} emails skipped due to user preference`);
    
  } catch (error) {
    console.error("[CRON] Error in generateDailyAwakenings:", error);
  }
};

cron("daily-awakening", 30 * 60, generateDailyAwakenings);

// Register organic email cron when feature is enabled
if (Reframe.env.FEAT_ORGANIC_EMAIL === "on") {
  cron("organic-email", 1 * 60, processOrganicEmails); // 2 minutes for testing
}

// Register daily digest cron job (runs every 30 minutes to check for 3pm in different timezones)
cron("daily-digest", 30 * 60, processDailyDigests);

// Register Limitless test cron when feature is enabled
if (Reframe.env.FEAT_LIMITLESS_TEST === "on") {
  // Run every 10 minutes for testing
  cron("limitless-test", 10 * 60, processLimitlessTest);
}

// Register coach digest cron job (runs every 30 minutes to check for 3pm in different timezones)
cron("coach-digest", 30 * 60, processCoachDailyDigests);

// hello world function cron

// cron("hello-world", 3, async () => {
//   console.log("hello world");
// });


self.addEventListener("error", (event) => {
  console.log("[Uncaught Error]", event.error.stack);
  event.preventDefault();
});

self.addEventListener("unhandledrejection", (event) => {
  event.promise.catch((error) =>
    console.log("[Unhandled Rejection]", error.stack)
  );
  event.preventDefault();
});