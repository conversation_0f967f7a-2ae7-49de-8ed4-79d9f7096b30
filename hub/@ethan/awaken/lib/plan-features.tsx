"use client";

import React from "npm:react@canary";
import { EssenceIcon } from "./icons.tsx";
import { PlanType } from "./plans.ts";

// Feature interface for consistent structure
export interface PlanFeature {
  icon: React.ReactNode;
  title: string;
  description: string;
}

// Basic plan features
export const BASIC_FEATURES: PlanFeature[] = [
  {
    icon: <EssenceIcon className="w-[min(48px,6vh)] h-[min(48px,6vh)] text-[#FF5727]" />, // show Awaken Essence flame
    title: "110 Essence for greater shifts in life & work",
    description: "110 minutes of calls or 550 messages with Kokoro (JP is 1.5x the rate)"
  },
  {
    icon: "❤️‍🔥",
    title: "Longer live calls",
    description: "Go deeper with calls up to 35 minutes long"
  },
  {
    icon: "🤝",
    title: "Partner with Awaken for deeper spiritual growth",
    description: "Beyond is your commitment to transformation in the ways that matter most"
  }
];

// Premium plan features
export const PREMIUM_FEATURES: PlanFeature[] = [
  {
    icon: <EssenceIcon className="w-[min(48px,6vh)] h-[min(48px,6vh)] text-[#FF5727]" />, // unified icon across plans
    title: "250 Essence for the most powerful shifts in life & work",
    description: "250 minutes of calls or 1250 messages with Kokoro (JP is 1.5x the rate)"
  },
  {
    icon: "❤️‍🔥",
    title: "Longest live calls",
    description: "Go deeper with calls up to 45 minutes long"
  },
  {
    icon: "🤝",
    title: "Partner with Awaken for deeper spiritual growth",
    description: "Boundless is your commitment to transformation in the ways that matter most"
  }
];

// Feature card component that can be reused for both plans
export const FeatureCard: React.FC<{
  features: PlanFeature[];
  planType: PlanType;
  title: string;
}> = ({ features, planType, title }) => {
  // Define consistent colors for each plan type with gradients
  const getPlanStyles = () => {
    if (planType === "basic") {
      return {
        mainColor: "#FCA311",
        titleColor: "text-[#FCA311]",
        glowColor: "rgba(252,163,17,0.15)",
        borderColor: "#3a332e",
        gradientFrom: "#442214",
        gradientVia: "#1D0E08",
        gradientTo: "#000000",
      };
    } else {
      return {
        mainColor: "#A020F0",
        titleColor: "text-[#ffffff]",
        glowColor: "rgba(138,43,226,0.15)",
        borderColor: "#3D2C5A",
        gradientFrom: "#2A1B3D",
        gradientVia: "#190E28",
        gradientTo: "#000000",
      };
    }
  };

  const styles = getPlanStyles();

  return (
    <div
      className={`
        relative rounded-2xl p-6
        overflow-hidden
        h-full
        w-full
        border border-[${styles.borderColor}]
        transition-all duration-300
      `}
      style={{
        boxShadow: `0 0 40px ${styles.glowColor}`,
        background: `linear-gradient(to bottom, ${styles.gradientFrom}, ${styles.gradientVia} 60%, ${styles.gradientTo})`,
      }}
    >
      {/* Top glow effect */}
      <div
        className="pointer-events-none absolute inset-0"
        style={{
          background: `radial-gradient(circle at top, ${styles.mainColor}30 0%, transparent 70%)`
        }}
      />
      
      {/* Subtle border glow */}
      <div
        className="absolute inset-0 rounded-2xl -z-10"
        style={{
          background: `linear-gradient(to bottom, ${styles.mainColor}40, transparent 70%)`,
          margin: '-1px',
          filter: 'blur(4px)'
        }}
      />
      
      <div className="text-center mb-[min(16px,2vh)]">
        <h2 className={`text-xl font-bold ${styles.titleColor}`}>
          Awaken {title}
        </h2>
      </div>
      
      {features.map((feature, index) => (
        <div key={index} className="flex items-start gap-3 mb-[min(16px,2vh)] last:mb-5">
          <div className="w-[min(32px,4vh)] h-[min(32px,4vh)] flex items-center justify-center shrink-0 
                          text-[min(1.5rem,3vh)]">
            {feature.icon}
          </div>
          <div className="flex-1">
            <p className="text-white text-[min(0.875rem,2vh)] font-medium">{feature.title}</p>
            <p className="text-white/75 text-[min(0.85rem,1.8vh)] mt-0.5">{feature.description}</p>
          </div>
        </div>
      ))}
    </div>
  );
}; 