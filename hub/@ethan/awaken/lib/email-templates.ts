"use server";

import Reframe from "@";

// Base URL for building links in emails. Falls back to production domain if not provided.
const APP_URL = Reframe.env.APP_URL ?? "https://awaken.is";

/**
 * Formats a message into HTML paragraphs, preserving formatting and identifying quotes
 * 
 * @param message - Raw message text
 * @returns Formatted HTML
 */
export const formatMessageToHtml = (message: string): string => {
  return message
    .trim()
    .split('\n\n')
    .filter(para => para.trim() !== '')
    .map(para => {
      // Check if this is a quote - a line that begins with ">" or has quotation marks
      if (para.trim().startsWith('>') || (para.includes('"') && para.includes('"'))) {
        return `<div class="quote">${para.trim().replace(/^>/, '')}</div>`;
      }
      return `<p>${para.trim()}</p>`;
    })
    .join('');
};

/**
 * Generates HTML for a daily awakening email
 * 
 * @param message - The daily awakening message content
 * @param userName - Optional user's name for personalization
 * @param dateString - Date string for the email header
 * @param subject - Optional subject line to use as email title (defaults to "Good Morning")
 * @returns Complete HTML email
 */
export const generateDailyAwakeningEmailHtml = (
  message: string, 
  userName?: string,
  dateString?: string,
  subject?: string
): string => {
  // Use current date if not provided
  const date = dateString || new Date().toLocaleDateString('en-US', { 
    weekday: 'long', 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric' 
  });

  // Format message for HTML
  const formattedMessage = formatMessageToHtml(message);
  const currentYear = new Date().getFullYear();
  
  return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Daily Awakening</title>
  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
      background-color: #121212;
      color: #e0e0e0;
    }
    .container {
      max-width: 600px;
      margin: 0 auto;
      padding: 30px 20px;
    }
    .header {
      text-align: center;
      margin-bottom: 25px;
    }
    .logo {
      width: 20px;
      height: 20px;
      margin-bottom: 0px;
    }
    .logo-container {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      margin-bottom: 30px;
    }
    .logo-text {
      font-size: 16px;
      font-weight: normal;
      color: #ffffff;
    }
    .email-title {
      font-size: 28px;
      font-weight: 500;
      color: #f8a13f;
      margin: 0;
      letter-spacing: 0.5px;
    }
    .date {
      font-size: 14px;
      color: #a3a3a3;
      margin-top: 5px;
    }
    .circle-image {
      text-align: center;
      margin: 25px 0;
    }
    .circle-img {
      width: 120px;
      height: 120px;
      border-radius: 50%;
      background: linear-gradient(135deg, rgba(248,161,63,0.2) 0%, rgba(230,123,16,0.2) 100%);
      padding: 5px;
    }
    .content-wrapper {
      background-color: rgba(20, 20, 20, 0.5);
      border-radius: 12px;
      padding: 30px;
      margin: 15px 0 25px;
      border: 1px solid rgba(248,161,63,0.15);
    }
    .content {
      font-size: 16px;
      line-height: 1.8;
      color: #e0e0e0;
    }
    .content p {
      margin-bottom: 20px;
    }
    .content p:last-child {
      margin-bottom: 0;
    }
    .highlight {
      color: #f3d39a;
      font-weight: 500;
    }
    .quote {
      font-style: italic;
      padding: 15px 20px;
      border-left: 3px solid #f8a13f;
      margin: 25px 0;
      background-color: rgba(248,161,63,0.05);
    }
    .action-container {
      text-align: center;
      margin: 30px 0;
    }
    .action-button {
      display: inline-block;
      padding: 14px 32px;
      background: linear-gradient(135deg, #f8a13f 0%, #e67b10 100%);
      color: #121212;
      text-decoration: none;
      border-radius: 30px;
      font-weight: 500;
      font-size: 16px;
      letter-spacing: 0.5px;
      transition: all 0.3s ease;
      box-shadow: 0 4px 12px rgba(248,161,63,0.3);
    }
    .action-button:hover {
      background: linear-gradient(135deg, #f8a13f 0%, #f8a13f 100%);
      transform: translateY(-2px);
      box-shadow: 0 6px 15px rgba(248,161,63,0.4);
    }
    .signature {
      text-align: right;
      font-size: 16px;
      color: #f8a13f;
      margin: 20px 0;
      font-weight: 500;
    }
    .footer {
      text-align: center;
      font-size: 12px;
      color: #a3a3a3;
      margin-top: 40px;
      padding-top: 20px;
      border-top: 1px solid rgba(248,161,63,0.15);
    }
    .social-icons {
      margin: 15px 0;
    }
    .social-icon {
      display: inline-block;
      width: 30px;
      height: 30px;
      margin: 0 8px;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <div class="logo-container">
        <svg
          width="20"
          height="20"
          viewBox="0 0 402 402"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          class="logo"
        >
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M257.697 310C297.014 289.498 323.857 248.358 323.857 200.947C323.857 133.066 268.828 78.0378 200.947 78.0378C133.066 78.0378 78.0378 133.066 78.0378 200.947C78.0378 248.358 104.881 289.498 144.198 310H32.1368C11.8047 278.591 0 241.148 0 200.947C0 89.9672 89.9672 0 200.947 0C311.928 0 401.895 89.9672 401.895 200.947C401.895 241.148 390.09 278.591 369.758 310H257.697ZM43.9298 326.36C48.6326 332.24 53.6589 337.851 58.9817 343.164H342.913C348.236 337.851 353.262 332.24 357.965 326.36H43.9298ZM102.792 376.332C93.9273 371.36 85.4781 365.736 77.5093 359.524H324.385C316.417 365.736 307.967 371.36 299.103 376.332H102.792ZM140.653 392.692C159.686 398.671 179.94 401.895 200.947 401.895C221.954 401.895 242.209 398.671 261.242 392.692H140.653Z"
            fill="url(#paint0_linear_3353_118)"
          />
          <defs>
            <linearGradient
              id="paint0_linear_3353_118"
              x1="200.947"
              y1="401.895"
              x2="200.947"
              y2="1.3068e-05"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#FF5727" />
              <stop offset="1" stop-color="#FFC360" />
            </linearGradient>
          </defs>
        </svg>
        <span class="logo-text">awaken</span>
      </div>
      <h1 class="email-title">${subject || "Good Morning"}</h1>
      <p class="date">${date}</p>
    </div>
    
    <div class="content-wrapper">
      <div class="content">

        ${formattedMessage}
      </div>
      <div class="signature" style="text-align: center;">
        - Kokoro
      </div>
    </div>
  
    <div class="action-container">
      <a href="${APP_URL}/chat" class="action-button">Reply</a>
    </div>
    
    <div class="footer">
      <p>© ${currentYear} Awaken. All rights reserved.</p>
      <p><a href="${APP_URL}/chat" style="color: #a3a3a3;">Manage Email Preferences in the Menu</a></p>
    </div>
  </div>
</body>
</html>`;
};

/**
 * Generates HTML for a gift email with Daily Awakening styling
 * 
 * @param recipientEmail - Recipient's email address
 * @param senderName - Name of the person sending the gift
 * @param essenceAmount - Amount of essence gifted
 * @param message - Optional personal message
 * @param code - Gift code for redemption
 * @param isAutoCredited - Whether the gift was automatically credited
 * @returns Complete HTML email
 */
export const generateGiftEmailHtml = (
  recipientEmail: string,
  senderName: string,
  essenceAmount: number,
  message?: string,
  code?: string,
  isAutoCredited: boolean = false
): string => {
  const currentYear = new Date().getFullYear();
  
  // Build the main content based on whether it's auto-credited
  let mainContent = '';
  
  if (isAutoCredited) {
    mainContent = `
      <p>Great news! <span class="highlight">${senderName}</span> has sent you a gift of <span class="highlight">${essenceAmount} Essence</span> for Awaken.</p>
      <p>We've already added it to your account balance, so you can start using it right away.</p>
      ${message ? `
        <div class="quote">
          <p>${senderName} included this message for you:</p>
          <p>"${message}"</p>
        </div>
      ` : ''}
    `;
  } else {
    mainContent = `
      <p><span class="highlight">${senderName}</span> has sent you a special gift!</p>
      <p>You've received <span class="highlight">${essenceAmount} Essence</span> to start your transformative journey with Awaken.</p>
      ${message ? `
        <div class="quote">
          <p>${senderName} says:</p>
          <p>"${message}"</p>
        </div>
      ` : ''}
      <div class="gift-code-section">
        <p>Your gift code:</p>
        <div class="gift-code">${code}</div>
      </div>
      <div class="what-is-awaken">
        <h3>What is Awaken?</h3>
        <p>Awaken AI walks with you 24/7, guiding you to love, presence, and inner power in the raw moments of daily life.</p>
      </div>
    `;
  }
  
  const buttonText = isAutoCredited ? 'Continue Your Journey' : 'Redeem Your Gift';
  const buttonUrl = isAutoCredited ? `${APP_URL}/chat` : `${APP_URL}/redeem?g=${code}`;
  
  return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Gift from ${senderName}</title>
  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
      background-color: #121212;
      color: #e0e0e0;
    }
    .container {
      max-width: 600px;
      margin: 0 auto;
      padding: 30px 20px;
    }
    .header {
      text-align: center;
      margin-bottom: 25px;
    }
    .logo {
      width: 20px;
      height: 20px;
      margin-bottom: 0px;
    }
    .logo-container {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      margin-bottom: 30px;
    }
    .logo-text {
      font-size: 16px;
      font-weight: normal;
      color: #ffffff;
    }
    .email-title {
      font-size: 28px;
      font-weight: 500;
      color: #f8a13f;
      margin: 0;
      letter-spacing: 0.5px;
    }
    .subtitle {
      font-size: 16px;
      color: #a3a3a3;
      margin-top: 10px;
    }
    .content-wrapper {
      background-color: rgba(20, 20, 20, 0.5);
      border-radius: 12px;
      padding: 30px;
      margin: 15px 0 25px;
      border: 1px solid rgba(248,161,63,0.15);
    }
    .content {
      font-size: 16px;
      line-height: 1.8;
      color: #e0e0e0;
    }
    .content p {
      margin-bottom: 20px;
    }
    .content p:last-child {
      margin-bottom: 0;
    }
    .highlight {
      color: #f8a13f;
      font-weight: 500;
    }
    .quote {
      font-style: italic;
      padding: 15px 20px;
      border-left: 3px solid #f8a13f;
      margin: 25px 0;
      background-color: rgba(248,161,63,0.05);
    }
    .quote p {
      margin: 10px 0;
    }
    .quote p:first-child {
      font-style: normal;
      font-size: 14px;
      color: #a3a3a3;
      margin-bottom: 15px;
    }
    .gift-code-section {
      text-align: center;
      margin: 30px 0;
    }
    .gift-code {
      display: inline-block;
      font-size: 24px;
      font-weight: 600;
      color: #f8a13f;
      background-color: rgba(248,161,63,0.1);
      padding: 15px 30px;
      border-radius: 8px;
      letter-spacing: 2px;
      margin-top: 10px;
      border: 1px solid rgba(248,161,63,0.3);
    }
    .what-is-awaken {
      margin-top: 30px;
      padding-top: 20px;
      border-top: 1px solid rgba(248,161,63,0.15);
    }
    .what-is-awaken h3 {
      color: #f8a13f;
      font-size: 18px;
      margin-bottom: 10px;
    }
    .action-container {
      text-align: center;
      margin: 30px 0;
    }
    .action-button {
      display: inline-block;
      padding: 14px 32px;
      background: linear-gradient(135deg, #f8a13f 0%, #e67b10 100%);
      color: #121212;
      text-decoration: none;
      border-radius: 30px;
      font-weight: 500;
      font-size: 16px;
      letter-spacing: 0.5px;
      transition: all 0.3s ease;
      box-shadow: 0 4px 12px rgba(248,161,63,0.3);
    }
    .action-button:hover {
      background: linear-gradient(135deg, #f8a13f 0%, #f8a13f 100%);
      transform: translateY(-2px);
      box-shadow: 0 6px 15px rgba(248,161,63,0.4);
    }
    .footer {
      text-align: center;
      font-size: 12px;
      color: #a3a3a3;
      margin-top: 40px;
      padding-top: 20px;
      border-top: 1px solid rgba(248,161,63,0.15);
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <div class="logo-container">
        <svg
          width="20"
          height="20"
          viewBox="0 0 402 402"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          class="logo"
        >
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M257.697 310C297.014 289.498 323.857 248.358 323.857 200.947C323.857 133.066 268.828 78.0378 200.947 78.0378C133.066 78.0378 78.0378 133.066 78.0378 200.947C78.0378 248.358 104.881 289.498 144.198 310H32.1368C11.8047 278.591 0 241.148 0 200.947C0 89.9672 89.9672 0 200.947 0C311.928 0 401.895 89.9672 401.895 200.947C401.895 241.148 390.09 278.591 369.758 310H257.697ZM43.9298 326.36C48.6326 332.24 53.6589 337.851 58.9817 343.164H342.913C348.236 337.851 353.262 332.24 357.965 326.36H43.9298ZM102.792 376.332C93.9273 371.36 85.4781 365.736 77.5093 359.524H324.385C316.417 365.736 307.967 371.36 299.103 376.332H102.792ZM140.653 392.692C159.686 398.671 179.94 401.895 200.947 401.895C221.954 401.895 242.209 398.671 261.242 392.692H140.653Z"
            fill="url(#paint0_linear_3353_118)"
          />
          <defs>
            <linearGradient
              id="paint0_linear_3353_118"
              x1="200.947"
              y1="401.895"
              x2="200.947"
              y2="1.3068e-05"
              gradientUnits="userSpaceOnUse"
            >
              <stop stop-color="#FF5727" />
              <stop offset="1" stop-color="#FFC360" />
            </linearGradient>
          </defs>
        </svg>
        <span class="logo-text">awaken</span>
      </div>
      <h1 class="email-title">You've Received a Gift!</h1>
      <p class="subtitle">A transformative journey awaits</p>
    </div>
    
    <div class="content-wrapper">
      <div class="content">
        ${mainContent}
      </div>
    </div>
  
    <div class="action-container">
      <a href="${buttonUrl}" class="action-button">${buttonText}</a>
    </div>
    
    <div class="footer">
      <p>© ${currentYear} Awaken. All rights reserved.</p>
      <p><a href="${APP_URL}" style="color: #a3a3a3;">${APP_URL.replace('https://', '')}</a></p>
    </div>
  </div>
</body>
</html>`;
}; 