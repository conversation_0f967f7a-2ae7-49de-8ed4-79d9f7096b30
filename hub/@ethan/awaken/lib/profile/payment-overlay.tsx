"use client"

import React from "npm:react@canary"
import { useState, useEffect, useRef, useCallback } from "npm:react@canary"
import { useSpring, animated, config } from "npm:@react-spring/web@9.7.3"
import { Button, Text } from "@reframe/ui/main.tsx"
import { CircleXIcon } from "@reframe/icons/circle-x.ts"
import { type PlanType, PLANS } from "../plans.ts"
import { CheckIcon } from "@reframe/icons/check.ts"
import { getStripePrices } from "../../action.ts"
import { UnreadIcon } from "@reframe/icons/unread.ts"
import { ChevronLeftIcon } from "@reframe/icons/chevron-left.ts"
import { ChevronRightIcon } from "@reframe/icons/chevron-right.ts"
import { FeatureCard, BASIC_FEATURES, PREMIUM_FEATURES } from "../plan-features.tsx"
import { upgradeSubscription } from "../../actions/stripe-actions.ts"
import { isExpoIOS } from "../platform-utils.ts"


interface Feature {
  icon: string
  title: string
  description: string
}

interface PaymentOverlayProps {
  isOpen: boolean
  onClose: () => void
  user: any
  userData?: any
  enrollmentMessage?: string
}

// Add this CSS class to hide scrollbars and enable smooth scrolling
const scrollableContentStyles = {
  scrollBehavior: 'smooth',
  msOverflowStyle: 'none', // IE and Edge
  scrollbarWidth: 'none', // Firefox
  '&::-webkit-scrollbar': { // Chrome, Safari and Opera
    display: 'none'
  }
} as const;

// Helper function to determine user's current plan type
const getCurrentPlanType = (userData: any): 'free' | 'basic' | 'premium' => {
  const planId = userData?.subscription?.planId;
  if (!planId || planId === 'free_plan') {
    return 'free';
  }
  if (planId.startsWith('basic_')) {
    return 'basic';
  }
  if (planId.startsWith('premium_')) {
    return 'premium';
  }
  return 'free'; // fallback
};

// Helper function to get available plans for user
const getAvailablePlans = (currentPlanType: 'free' | 'basic' | 'premium'): PlanType[] => {
  switch (currentPlanType) {
    case 'free':
      return ['basic', 'premium'];
    case 'basic':
      return ['premium'];
    case 'premium':
      return []; // Premium users can only cancel, no upgrades
    default:
      return ['basic', 'premium'];
  }
};

export const PaymentOverlay: React.FC<PaymentOverlayProps> = ({ isOpen, onClose, user, userData, enrollmentMessage }) => {
  const [isYearly, setIsYearly] = useState(true);
  const [loading, setLoading] = useState<PlanType | false>(false);
  const [error, setError] = useState<string | null>(null);
  const [prices, setPrices] = useState<any>(null);
  const [activePlanIndex, setActivePlanIndex] = useState(1);
  const [touchStart, setTouchStart] = useState(0);
  const [touchEnd, setTouchEnd] = useState(0);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const planCardsRef = useRef<HTMLDivElement>(null);

  // Get current plan type and available plans
  const currentPlanType = getCurrentPlanType(userData);
  const hasActiveSubscription = (userData?.subscription?.id || userData?.stripeSubscriptionId) && currentPlanType !== 'free';

  const overlayAnimation = useSpring({
    opacity: isOpen ? 1 : 0,
    config: config.default
  })

  const modalAnimation = useSpring({
    transform: isOpen ? 'translateY(0%)' : 'translateY(100%)',
    config: { ...config.default, tension: 200, friction: 20 }
  })

  const toggleAnimation = useSpring({
    x: isYearly ? 20 : 0,
    config: { tension: 500, friction: 30 }
  })

  const isIosExpo = isExpoIOS();

  useEffect(() => {
    if (isOpen) {
      const fetchPrices = async () => {
        try {
          const stripePrices = await getStripePrices();
          setPrices(stripePrices);
        } catch (err) {
          console.error("Error fetching prices:", err);
          setError("Failed to load pricing information");
        }
      };
      fetchPrices();
    }
  }, [isOpen]);

  const handleSubscribe = async (planType: PlanType) => {
    console.log("[PAYMENT] Starting subscription flow:", { planType, currentPlanType, hasActiveSubscription });

    try {
      setLoading(planType)
      setError(null)

      const formattedPlanId = `${planType}_plan_${isYearly ? "yearly" : "monthly"}`

      if (hasActiveSubscription) {
        console.log("[PAYMENT] Upgrading existing subscription");
        
        // Handle upgrade
        const response = await upgradeSubscription(
          user.channelId.toString(),
          formattedPlanId
        );

        console.log("[PAYMENT] Upgrade response:", response);

        if (!response || !response.success) {
          console.error("[PAYMENT] Upgrade failed:", response);
          setError("Failed to upgrade subscription. Please try again.");
          return;
        }

        console.log("[PAYMENT] Upgrade successful, redirecting to success page");
        
        // Reload page with success parameters to show subscription success UI
        window.location.href = `/chat?subscription_success=true&plan=${planType}`;
        return; // Important: return here to prevent further execution
      } else {
        console.log("[PAYMENT] Creating new subscription");
        // Handle new subscription
        const payload = {
          email: user.email,
          channelId: user.channelId.toString(),
          plan: formattedPlanId,
          isYearly,
        }

        const response = await fetch("/api/create-checkout-session", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(payload),
        })

        if (!response.ok) {
          throw new Error(await response.text() || "Failed to create checkout session")
        }

        const data = await response.json()
        if (!data.url) {
          throw new Error("Invalid checkout session response")
        }

        globalThis.location.href = data.url
      }
    } catch (err) {
      console.error("[PAYMENT] Error:", err);
      setError(err instanceof Error ? err.message : "Failed to start subscription");
    } finally {
      setLoading(false);
    }
  }

  // Swipe logic for plan cards
  const handleSwipeLeft = () => {
    if (activePlanIndex > 0) {
      setActivePlanIndex(activePlanIndex - 1);
    }
  };

  const handleSwipeRight = () => {
    if (activePlanIndex < 1) {
      setActivePlanIndex(activePlanIndex + 1);
    }
  };

  // Improved touch handling with bounds checking
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchStart(e.targetTouches[0].clientX);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = () => {
    if (touchStart - touchEnd > 50) {
      // Swiped left - but only if we're not already at the rightmost card
      if (activePlanIndex < 1) {
        handleSwipeRight();
      }
    }

    if (touchStart - touchEnd < -50) {
      // Swiped right - but only if we're not already at the leftmost card
      if (activePlanIndex > 0) {
        handleSwipeLeft();
      }
    }
    
    // Reset touch positions after handling
    setTouchStart(0);
    setTouchEnd(0);
  };

  const getActivePlan = (): PlanType => {
    return activePlanIndex === 0 ? "basic" : "premium";
  };

  // Helper functions to determine plan states
  const isPlanCurrent = (planType: PlanType): boolean => {
    return currentPlanType === planType;
  };

  const isPlanAvailable = (planType: PlanType): boolean => {
    if (currentPlanType === 'free') return true; // Free users can choose any plan
    if (currentPlanType === 'basic' && planType === 'premium') return true; // Basic can upgrade to premium
    return false; // Premium users can't downgrade, current plan is not available for purchase
  };

  const getPlanButtonText = (planType: PlanType): string => {
    if (isPlanCurrent(planType)) return "Current Plan";
    if (currentPlanType === 'free') return "Let's do it";
    if (currentPlanType === 'basic' && planType === 'premium') return "Upgrade Now";
    return "Not Available";
  };

  const shouldShowButton = (planType: PlanType): boolean => {
    if (currentPlanType === 'free') return getActivePlan() === planType; // Free users see button on active plan only
    return isPlanAvailable(planType); // Subscribed users see button only on available plans
  };

  if (!isOpen) return null

  return (
    <>
      {isOpen && (
        <>
          <animated.div
            style={overlayAnimation}
            className="fixed inset-0 bg-black/80 backdrop-blur-sm z-40"
            onClick={onClose}
          />
          <animated.div
            className="fixed inset-0 z-50 flex justify-center items-start overflow-hidden"
            style={modalAnimation}
          >
            <div className="w-full h-full sm:max-w-[400px] md:max-w-xl lg:max-w-2xl bg-black relative flex flex-col">
              <div className="flex-none">
                <Button
                  variant="ghost"
                  css="absolute right-2 top-2 p-2 text-white hover:bg-transparent z-50"
                  onClick={onClose}
                >
                  <CircleXIcon className="w-6 h-6" />
                </Button>
              </div>

              <div 
                ref={scrollContainerRef}
                className="flex-1 overflow-y-auto px-6 min-h-0"
                style={{
                  ...scrollableContentStyles,
                  WebkitOverflowScrolling: 'touch'
                }}
              >
                <div className="px-6 pt-[min(48px,5vh)] pb-[min(24px,3vh)]">
                  <h1 className="text-2xl font-medium text-white mb-[min(15px,1.6vh)] text-center">
                    {currentPlanType === 'free' ? 'Awaken your fullest life' : 'Choose your plan'}
                  </h1>
                    {enrollmentMessage && (
                      <div className="text-[#FCA311] text-base mt-2 mb-0">
                        {enrollmentMessage.split(". ").map((sentence, index, array) => (
                          <p key={index} className={index < array.length - 1 ? "mb-3" : ""}>
                            {index === 0 ? `"${sentence}` : sentence}
                            {index === array.length - 1 ? `"` : "."}
                          </p>
                        ))}
                      </div>
                    )}
                </div>
                {/* Always show both plans with appropriate states */}
                {/* Mobile (swipe) */}
                <div className="relative md:hidden">
                  {/* Navigation Tabs */}
                  <div className="flex justify-center space-x-2 mb-4">
                    <button
                      className={`px-3 py-1 rounded-full text-xs font-medium transition-colors duration-300 ${
                        activePlanIndex === 0 
                          ? 'bg-[#FCA311] text-black' 
                          : isPlanCurrent('basic')
                            ? 'bg-[#FCA311]/20 text-[#FCA311] border border-[#FCA311]'
                            : 'bg-gray-700 text-white/80'
                      }`}
                      onClick={() => setActivePlanIndex(0)}
                    >
                      BEYOND {isPlanCurrent('basic') && '(Current)'}
                    </button>
                    <button
                      className={`px-3 py-1 rounded-full text-xs font-medium transition-colors duration-300 ${
                        activePlanIndex === 1 
                          ? 'bg-[#A020F0] text-black' 
                          : isPlanCurrent('premium')
                            ? 'bg-[#A020F0]/20 text-[#A020F0] border border-[#A020F0]'
                            : 'bg-gray-700 text-white/80'
                      }`}
                      onClick={() => setActivePlanIndex(1)}
                    >
                      BOUNDLESS {isPlanCurrent('premium') && '(Current)'}
                    </button>
                  </div>

                  {/* Left/Right Navigation Buttons */}
                  {activePlanIndex > 0 && (
                    <button
                      className="absolute -left-2 top-1/2 transform -translate-y-1/2 z-10 bg-white/20 rounded-full p-2 transition-all duration-300 hover:bg-white/30"
                      onClick={handleSwipeLeft}
                    >
                      <ChevronLeftIcon className="w-7 h-7 text-white/90" />
                    </button>
                  )}

                  {activePlanIndex < 1 && (
                    <button
                      className="absolute -right-2 top-1/2 transform -translate-y-1/2 z-10 bg-white/20 rounded-full p-2 transition-all duration-300 hover:bg-white/30"
                      onClick={handleSwipeRight}
                    >
                      <ChevronRightIcon className="w-7 h-7 text-white/90" />
                    </button>
                  )}

                  {/* Swipe container */}
                  <div className="overflow-hidden">
                    <div
                      ref={planCardsRef}
                      className="flex"
                      style={{
                        width: '170vw',
                        transform: `translateX(-${activePlanIndex * 85}vw)`,
                        transition: 'transform 300ms ease-out'
                      }}
                      onTouchStart={handleTouchStart}
                      onTouchMove={handleTouchMove}
                      onTouchEnd={handleTouchEnd}
                    >
                      {/* Basic Plan Card */}
                      <div className={`flex-shrink-0 w-[85vw] px-2 ${!isPlanAvailable('basic') && !isPlanCurrent('basic') ? 'opacity-50' : ''}`}>
                        <div className="relative">
                          <FeatureCard
                            features={BASIC_FEATURES}
                            planType="basic"
                            title="BEYOND"
                          />
                          {isPlanCurrent('basic') && (
                            <div className="absolute top-2 right-2 bg-[#FCA311] text-black text-xs font-bold px-2 py-1 rounded-full">
                              CURRENT
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Premium Plan Card */}
                      <div className={`flex-shrink-0 w-[85vw] px-2 ${!isPlanAvailable('premium') && !isPlanCurrent('premium') ? 'opacity-50' : ''}`}>
                        <div className="relative">
                          <FeatureCard
                            features={PREMIUM_FEATURES}
                            planType="premium"
                            title="BOUNDLESS"
                          />
                          {isPlanCurrent('premium') && (
                            <div className="absolute top-2 right-2 bg-[#A020F0] text-white text-xs font-bold px-2 py-1 rounded-full">
                              CURRENT
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Desktop (side-by-side) */}
                <div className="hidden md:flex justify-center gap-6 mb-8">
                  {/* Basic Plan */}
                  <div
                    className={`w-1/2 max-w-sm cursor-pointer transition-all duration-200 rounded-2xl relative ${
                      activePlanIndex === 0 
                        ? 'ring-2 ring-[#FCA311] scale-105' 
                        : 'opacity-70 hover:opacity-100'
                    } ${!isPlanAvailable('basic') && !isPlanCurrent('basic') ? 'opacity-30' : ''}`}
                    onClick={() => setActivePlanIndex(0)}
                  >
                    <FeatureCard
                      features={BASIC_FEATURES}
                      planType="basic"
                      title="BEYOND"
                    />
                    {isPlanCurrent('basic') && (
                      <div className="absolute top-2 right-2 bg-[#FCA311] text-black text-xs font-bold px-2 py-1 rounded-full">
                        CURRENT
                      </div>
                    )}
                  </div>

                  {/* Premium Plan */}
                  <div
                    className={`w-1/2 max-w-sm cursor-pointer transition-all duration-200 rounded-2xl relative ${
                      activePlanIndex === 1 
                        ? 'ring-2 ring-[#A020F0] scale-105' 
                        : 'opacity-70 hover:opacity-100'
                    } ${!isPlanAvailable('premium') && !isPlanCurrent('premium') ? 'opacity-30' : ''}`}
                    onClick={() => setActivePlanIndex(1)}
                  >
                    <FeatureCard
                      features={PREMIUM_FEATURES}
                      planType="premium"
                      title="BOUNDLESS"
                    />
                    {isPlanCurrent('premium') && (
                      <div className="absolute top-2 right-2 bg-[#A020F0] text-white text-xs font-bold px-2 py-1 rounded-full">
                        CURRENT
                      </div>
                    )}
                  </div>
                </div>

                {isIosExpo && (
                  <div className="px-6 mb-6 mt-2">
                    <div className="bg-[#FCA311]/20 p-4 rounded-lg text-center">
                      <Text className="text-[#FCA311] font-medium">
                        Upgrading your plan isn’t supported in this app. We know this isn’t ideal.
                      </Text>
                    </div>
                  </div>
                )}

                <div className="text-center mt-6 mb-6">
                  <p className="text-white/100 text-[min(0.875rem,2vh)] font-bold">
                    "What the fuck, this was freaking awesome. I got the chills and a profound breakthrough."
                  </p>
                  <p className="text-white/80 text-[min(0.875rem,2vh)] mt-1">- Astrid Deneve, Coach</p>
                </div>
                <div className="text-center mt-6 mb-6">
                  <p className="text-white/100 text-[min(0.875rem,2vh)] font-bold">
                    "Probably one of the best sessions I've had in my life. This is either a blood brilliant tool or I've hired the wrong coaches in the past."
                  </p>
                  <p className="text-white/80 text-[min(0.875rem,2vh)] mt-1">- Paolo Valteroni, Coach</p>
                </div>
                <div className="text-center mt-6 mb-6">
                  <p className="text-white/100 text-[min(0.875rem,2vh)] font-bold">
                    "I just wanted to say that Awaken is life changing. I am using it on a daily basis and learning so much. I just run a prospect client whatsapp conversation through it which resulted in him signing up for 2500 Euros."
                  </p>
                  <p className="text-white/80 text-[min(0.875rem,2vh)] mt-1">- Dr. Matthias Hombauer, Coach</p>
                </div>
                <div className="text-center mt-6 mb-6">
                  <p className="text-white/100 text-[min(0.875rem,2vh)] font-bold">
                    "Awaken really brought out the truth that I already knew, I felt it in my body and now I really know it. It feels like a turbo button… I'm able to ask and get that response right away, and then re-respond to get more depth."
                  </p>
                  <p className="text-white/80 text-[min(0.875rem,2vh)] mt-1">- Dan Sloan, CTO</p>
                </div>
                <div className="text-center mt-6 mb-6">
                  <p className="text-white/100 text-[min(0.875rem,2vh)] font-bold">
                    "Holy fucking shit man! That was super cool. I cried, I learned, I grew. It felt so much more realistic than I thought it would. Uncanny, just like Dan Sloan said. Remarkable really."
                  </p>
                  <p className="text-white/80 text-[min(0.875rem,2vh)] mt-1">- Ian Tompkins, Coach</p>
                </div>
                <div className="text-center mt-6 mb-6">
                  <p className="text-white/100 text-[min(0.875rem,2vh)] font-bold">
                    "It created a result. The questions that the AI asked, the reflection, the mirroring, what it saw about me, it was profound. It really helped me go into the places I needed to go… helping me access the uncomfortable places within that I have to look at and confront."
                  </p>
                  <p className="text-white/80 text-[min(0.875rem,2vh)] mt-1">- Kevin Fornier, CEO Coach</p>
                </div>
                <div className="text-center mt-6 mb-6">
                  <p className="text-white/100 text-[min(0.875rem,2vh)] font-bold">
                    "I really feel like I'm talking to [the actual] JP - love, money, freedom - it's all there."
                  </p>
                  <p className="text-white/80 text-[min(0.875rem,2vh)] mt-1">- Eddie Vaisman, CTO</p>
                </div>
                <div className="text-center mt-6 mb-6">
                  <p className="text-white/100 text-[min(0.875rem,2vh)] font-bold">
                    "So i've been using it for the past 45 minutes about this situation and it's got me in tears. This thing is remarkable - and i will tell you.... i created my own custom coaching GPT myself earlier this year, but obviously the training data is important - i can hear your voice in this"
                  </p>
                  <p className="text-white/80 text-[min(0.875rem,2vh)] mt-1">- Daniel Patterson, CEO</p>
                </div>
                <div className="text-center mt-6 mb-6">
                  <p className="text-white/100 text-[min(0.875rem,2vh)] font-bold">
                    "I just had a beautiful session with JP AI. It guided me into one of the core wounds I had, which was about, performing and being seen or being judged. Oh yeah, it, it was really cool, and I had tears and tingles."
                  </p>
                  <p className="text-white/80 text-[min(0.875rem,2vh)] mt-1">- Ethan Tan</p>
                </div>
                <div className="text-center mt-6 mb-6">
                  <p className="text-white/100 text-[min(0.875rem,2vh)] font-bold">
                    "Kokoro itself is great. I like how aggressive it can get when disagreeing with me. Normally you would expect it to adjust to your own opinion (at least ChatGPT does that) to make you feel better or something of sorts. I like how Kokoro doesn't give a damn in a way. I don't always agree with it, but this gives him quite a character, which I find absolutely insane for an Al"
                  </p>
                  <p className="text-white/80 text-[min(0.875rem,2vh)] mt-1">- Margarita</p>
                </div>

                <div className="text-center mt-6 mb-6">
                  <p className="text-white/100 text-[min(0.875rem,2vh)] font-bold">
                    "Awaken is more valuable than 99% of the apps that I have in my life."
                  </p>
                  <p className="text-white/80 text-[min(0.875rem,2vh)] mt-1">- Tom Sedge, Coach</p>
                </div>
                <div className="text-center mt-6 mb-6">
                  <p className="text-white/100 text-[min(0.875rem,2vh)] font-bold">
                    "I've really appreciated how [Awaken] has been making me live life more freely... I want to share this great tool with everyone"
                  </p>
                  <p className="text-white/80 text-[min(0.875rem,2vh)] mt-1">- Jivtesh Singh, Tech Architect</p>
                </div>
                <div className="text-center mt-6 mb-6">
                  <p className="text-white/100 text-[min(0.875rem,2vh)] font-bold">
                    "Awaken gave me the strength to keep going. Thank you from the bottom of my heart. It is already making a real difference"
                  </p>
                  <p className="text-white/80 text-[min(0.875rem,2vh)] mt-1">- Vale</p>
                </div>

                {/* Links to Terms and Privacy Policy */}
                <div className="flex justify-center gap-4 mb-6">
                  <a 
                    href="/terms" 
                    className="text-white/40 text-[min(0.875rem,2vh)] hover:text-white/60 transition-colors"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    Terms of Service
                  </a>
                  <a 
                    href="/privacy-policy" 
                    className="text-white/40 text-[min(0.875rem,2vh)] hover:text-white/60 transition-colors"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    Privacy Policy
                  </a>
                </div>

                <div className="pb-4" />
              </div>

              <div className={`${isIosExpo ? 'hidden' : ''} flex-none bg-black/95 border-t border-[#FCA31133] backdrop-blur-sm`}>
                <div className="px-6 py-[min(8px,1vh)]">
                  <div
                    className="
                      relative rounded-2xl p-[min(12px,1.5vh)]
                      bg-gradient-to-b from-[#1E1E1E] via-[#241B18] to-black
                      shadow-[0_0_50px_12px_rgba(255,87,39,0.1),0_0_100px_20px_rgba(255,87,39,0.07)]
                      border border-[#3a332e]

                      before:absolute before:inset-0 before:-z-10 before:rounded-2xl
                      before:p-[2px]
                      before:bg-gradient-to-r before:from-[#FF5727]/60 before:via-[#FFC360]/60 before:to-[#FF5727]/60
                      before:blur-xl

                      after:absolute after:inset-0 after:-z-20 after:rounded-2xl
                      after:p-[2px]
                      after:bg-gradient-to-r after:from-[#FF5727]/20 after:via-[#FFC360]/20 after:to-[#FF5727]/20
                      after:blur-2xl

                      overflow-hidden
                    "
                  >
                    {/* optional subtle radial glow */}
                    <div className="absolute inset-0 pointer-events-none 
                                    bg-[radial-gradient(circle_at_center,_rgba(255,87,39,0.05)_0%,_rgba(0,0,0,0)_80%)]" />

                    <div className="relative">
                      <div className="flex flex-col items-center text-center gap-[min(6px,0.8vh)]">
                        <div className="flex items-center justify-center gap-2 mb-3">
                          <span className={`${!isYearly ? "text-[#FCA311] font-bold" : "text-white/60"} text-[min(0.875rem,1.8vh)]`}>
                            monthly
                          </span>
                          <div
                            className="w-10 h-5 bg-black/30 border border-[#FCA311] rounded-full relative cursor-pointer"
                            onClick={() => setIsYearly(!isYearly)}
                          >
                            <animated.div
                              className="absolute top-[1px] left-[1px] w-4 h-4 bg-[#FCA311] rounded-full"
                              style={toggleAnimation}
                            />
                          </div>
                          <span className={`${isYearly ? "text-[#FCA311] font-bold" : "text-white/60"} text-[min(0.875rem,1.8vh)]`}>
                            yearly (-20%)
                          </span>
                        </div>

                        {!prices ? (
                          <div className="flex items-center justify-center gap-2">
                            <UnreadIcon css="text-2xl text-[#FCA311] w-6 h-6 animate-spin" />
                            <Text css="text-[#FCA311]">Loading prices...</Text>
                          </div>
                        ) : (
                          <div className="flex flex-col items-center gap-1">
                            <div className="flex items-baseline justify-center gap-1">
                              <span className="text-[#FCA311] text-[min(1.5rem,3vh)] font-semibold">
                                ${prices ? (isYearly ? 
                                  (prices[`${getActivePlan()}_plan`].price.yearly / 12).toFixed(0): 
                                  prices[`${getActivePlan()}_plan`].price.monthly.toFixed(0)) : '...'}
                              </span>
                              <span className="text-white text-[min(0.875rem,1.8vh)]">
                                / {isYearly ? 'month' : 'month'}
                              </span>
                            </div>
                          </div>
                        )}

                        {shouldShowButton(getActivePlan()) && (
                          <Button
                            css={`
                              w-full
                              mt-4
                              ${isPlanCurrent(getActivePlan()) || isIosExpo 
                                ? 'bg-gray-600 text-gray-300 cursor-not-allowed' 
                                : 'bg-[#FCA311] text-white hover:bg-[#FCA311]/90'
                              }
                              font-medium
                              text-lg
                              py-3
                              rounded-md

                              /* Add a subtle white glow around the button */
                              shadow-[0_0_12px_rgba(255,255,255,0.4)]
                              hover:shadow-[0_0_18px_rgba(255,255,255,0.6)]
                              transition-shadow
                            `}
                            onClick={() => handleSubscribe(getActivePlan())}
                            disabled={!!loading || isPlanCurrent(getActivePlan()) || isIosExpo}
                          >
                            {loading ? (
                              <span className="flex items-center justify-center">
                                <UnreadIcon css="text-xl text-white w-5 h-5 animate-spin mr-2" />
                                Processing...
                              </span>
                            ) : (
                              isIosExpo ? 'Unavailable on iOS' : getPlanButtonText(getActivePlan())
                            )}
                          </Button>
                        )}

                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {error && (
                <div className="absolute top-4 left-1/2 transform -translate-x-1/2 bg-red-500 text-white px-4 py-2 rounded-md">
                  {error}
                </div>
              )}
            </div>
          </animated.div>
        </>
      )}
    </>
  )
}