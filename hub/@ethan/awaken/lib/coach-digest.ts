"use server";
import Reframe from "@";
import { db } from "./db.ts";
import { v4 as uuid } from "npm:uuid";
import CryptoJS from "npm:crypto-js";

const secretKey = Reframe.env.SECRET_KEY;

/* -------------------------------------------------------------------------- */
/*                                  Types                                     */
/* -------------------------------------------------------------------------- */

export interface UnreadMessage {
  id: string;
  content: string;
  date: string;
  channelId: number;
  userName: string;
  messageType: string;
}

export interface UserDigestForCoach {
  channelId: number;
  userName: string;
  messageCount: number;
  messages: UnreadMessage[];
}

export interface CoachDigest {
  coachName: string;
  email: string;
  totalMessageCount: number;
  userCount: number;
  users: UserDigestForCoach[];
}

/* -------------------------------------------------------------------------- */
/*                        Helper / Internal Functions                         */
/* -------------------------------------------------------------------------- */

/**
 * Get unread 'coach_message' messages for a specific coach within a time window.
 * Returns null if there are no unread messages.
 */
export const getUnreadCoachMessagesForDigest = async (
  coachName: string,
  windowStart: string,
  windowEnd: string,
): Promise<CoachDigest | null> => {
  try {
    console.log(`[COACH DIGEST] Fetching unread messages for coach ${coachName} between ${windowStart} and ${windowEnd}`);

    // Fetch coach email first
    const coach = await db
      .selectFrom("coaches")
      .select(["email"])
      .where("name", "=", coachName)
      .executeTakeFirst();

    if (!coach || !coach.email) {
      console.log(`[COACH DIGEST] Coach ${coachName} has no email set – skipping`);
      return null;
    }

    const messages = await db
      .selectFrom("conversation")
      .select(["id", "content", "date", "channelId", "messageType", "sender", "coachName"])
      .where("coachName", "=", coachName)
      .where("sender", "=", "user")
      .where("messageType", "=", "coach_message")
      .where((eb) => eb.or([
        eb("seenByCoach", "=", 0),
        eb("seenByCoach", "is", null),
      ]))
      .where("date", ">=", windowStart)
      .where("date", "<=", windowEnd)
      .orderBy("date", "asc")
      .execute();

    if (messages.length === 0) {
      return null;
    }

    // Get user names for channelIds involved
    const channelIds = [...new Set(messages.map((m) => m.channelId))];

    const users = await db
      .selectFrom("user")
      .select(["channelId", "name"])
      .where("channelId", "in", channelIds as number[])
      .execute();

    const nameLookup: Record<number, string> = {};
    users.forEach((u) => {
      nameLookup[u.channelId] = u.name?.split(" ")[0] || "User";
    });

    // Decrypt and group by channelId
    const userGroups: Record<number, UserDigestForCoach> = {};

    for (const msg of messages) {
      let decrypted: string;
      try {
        decrypted = CryptoJS.AES.decrypt(msg.content, secretKey).toString(CryptoJS.enc.Utf8);
      } catch (e) {
        console.error(`[COACH DIGEST] Failed to decrypt message ${msg.id}`, e);
        continue;
      }

      if (!userGroups[msg.channelId]) {
        userGroups[msg.channelId] = {
          channelId: msg.channelId,
          userName: nameLookup[msg.channelId] || `User ${msg.channelId}`,
          messageCount: 0,
          messages: [],
        };
      }

      userGroups[msg.channelId].messages.push({
        id: msg.id,
        content: decrypted,
        date: msg.date,
        channelId: msg.channelId,
        userName: userGroups[msg.channelId].userName,
        messageType: msg.messageType,
      });

      userGroups[msg.channelId].messageCount++;
    }

    const usersArray = Object.values(userGroups);
    const totalMessageCount = usersArray.reduce((sum, u) => sum + u.messageCount, 0);

    return {
      coachName,
      email: coach.email,
      totalMessageCount,
      userCount: usersArray.length,
      users: usersArray,
    };
  } catch (error) {
    console.error(`[COACH DIGEST] Error building digest for coach ${coachName}:`, error);
    return null;
  }
};

/**
 * Check if a coach digest has already been sent for a coach on a given date (YYYY-MM-DD)
 */
export const hasCoachDigestBeenSent = async (
  coachName: string,
  digestDate: string,
): Promise<boolean> => {
  try {
    const existing = await db
      .selectFrom("dailyDigestJob")
      .select(["id"])
      .where("userId", "=", coachName) // reuse column
      .where("digestDate", "=", digestDate)
      .executeTakeFirst();
    return !!existing;
  } catch (error) {
    console.error(`[COACH DIGEST] Error checking sent record`, error);
    return false;
  }
};

/**
 * Record that a coach digest has been sent
 */
export const recordCoachDigestSent = async (
  coachName: string,
  digestDate: string,
  messageCount: number,
  userCount: number,
): Promise<void> => {
  try {
    const id = uuid();
    const now = new Date().toISOString();
    await db
      .insertInto("dailyDigestJob")
      .values({
        id,
        userId: coachName, // store coachName
        digestDate,
        sentAt: now,
        messageCount,
        coachCount: userCount, // repurpose field
        createdAt: now,
      })
      .execute();
  } catch (error) {
    console.error(`[COACH DIGEST] Error recording sent digest`, error);
  }
};

/* -------------------------------------------------------------------------- */
/*                        Email Subject / Body Builders                       */
/* -------------------------------------------------------------------------- */

export const generateCoachDigestSubject = (digest: CoachDigest): string => {
  const { totalMessageCount, userCount } = digest;
  const msgTxt = totalMessageCount === 1 ? "message" : "messages";
  const userTxt = userCount === 1 ? "user" : "users";
  return `You have ${totalMessageCount} new ${msgTxt} from ${userCount} ${userTxt}`;
};

export const generateCoachDigestEmailBody = (
  digest: CoachDigest,
  localDate: string,
): string => {
  const { totalMessageCount, userCount, users } = digest;

  let body = `Hi ${digest.coachName},\n\n`;
  body += `You received **${totalMessageCount} new message${totalMessageCount === 1 ? "" : "s"}** from ${userCount} user${userCount === 1 ? "" : "s"} on **${localDate}**.\n\n`;
  body += `Here’s a breakdown by user:\n\n`;

  for (const u of users) {
    body += `---\n\n`;
    body += `**${u.userName}**  (${u.messageCount} new)\n`;
    for (const m of u.messages) {
      const time = new Date(m.date).toLocaleTimeString("en-US", {
        hour: "numeric",
        minute: "2-digit",
        hour12: true,
      });
      const truncated = m.content.length > 80 ? `${m.content.slice(0, 77)}…` : m.content;
      body += `- ${time} — “${truncated}”\n`;
    }
    body += `\n`;
  }

  body += `---\n\n`;
  body += `Open Awaken admin to reply or mark as read.\n\n`;
  body += `To awakening 🌟`;
  return body;
};

/* -------------------------------------------------------------------------- */
/*                          Coach List for Digest                             */
/* -------------------------------------------------------------------------- */

export const getCoachesForDigest = async (): Promise<Array<{ name: string; email: string }>> => {
  const coaches = await db
    .selectFrom("coaches")
    .select(["name", "email"])
    .where("email", "is not", null)
    .execute();
  return coaches;
};
