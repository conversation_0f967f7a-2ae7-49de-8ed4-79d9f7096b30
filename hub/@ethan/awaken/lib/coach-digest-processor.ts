"use server";
import Reframe from "@";

import {
  getCoachesForDigest,
  getUnreadCoachMessagesForDigest,
  hasCoachDigestBeenSent,
  recordCoachDigestSent,
  generateCoachDigestSubject,
  generateCoachDigestEmailBody,
  type CoachDigest,
} from "./coach-digest.ts";
import { sendSimpleEmail } from "./email-sender.ts";
import { getUserInfo } from "./get-user-info.ts";

/* -------------------------------------------------------------------------- */
/*                               Time Helpers                                 */
/* -------------------------------------------------------------------------- */

export const isThreePmInTimezone = (timezone: string): boolean => {
  try {
    const now = new Date();
    const userTime = new Intl.DateTimeFormat("en-US", {
      timeZone: timezone,
      hour: "numeric",
      hour12: false,
    }).format(now);
    return parseInt(userTime) === 15;
  } catch (e) {
    console.error(`[COACH DIGEST] Time check error for tz ${timezone}`, e);
    return false;
  }
};

export const getLocalDateString = (timezone: string): string => {
  try {
    return new Intl.DateTimeFormat("en-US", {
      timeZone: timezone,
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
    }).format(new Date());
  } catch {
    return new Date().toLocaleDateString("en-US", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  }
};

export const getDigestTimeWindow = (
  timezone: string,
): { windowStart: string; windowEnd: string } => {
  try {
    const now = new Date();
    const localNow = new Date(now.toLocaleString("en-US", { timeZone: timezone }));
    const threePmToday = new Date(localNow);
    threePmToday.setHours(15, 0, 0, 0);
    const threePmYesterday = new Date(threePmToday);
    threePmYesterday.setDate(threePmYesterday.getDate() - 1);
    const windowStart = new Date(threePmYesterday.getTime() - threePmYesterday.getTimezoneOffset() * 60000).toISOString();
    // const windowEnd = new Date(threePmToday.getTime() - threePmToday.getTimezoneOffset() * 60000).toISOString();
    const windowEnd = new Date().toISOString();
    return { windowStart, windowEnd };
  } catch (e) {
    console.error(`[COACH DIGEST] Time window error`, e);
    const end = new Date().toISOString();
    const start = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();
    return { windowStart: start, windowEnd: end };
  }
};

/* -------------------------------------------------------------------------- */
/*                          Per-coach processing                              */
/* -------------------------------------------------------------------------- */

export const processCoachDigest = async (coach: {
  name: string;
  email: string;
}): Promise<{ success: boolean; skipped?: string; emailSent?: boolean; error?: string }> => {
  try {
    const coachInfo = await getUserInfo({ email: coach.email });
    const timezone = coachInfo?.timezone || "Europe/London";

    if (!isThreePmInTimezone(timezone)) {
      return { success: true, skipped: "Not 3pm yet" };
    }

    const today = new Date().toISOString().split("T")[0];
    if (await hasCoachDigestBeenSent(coach.name, today)) {
      return { success: true, skipped: "Already sent today" };
    }

    const { windowStart, windowEnd } = getDigestTimeWindow(timezone);
    const digest = await getUnreadCoachMessagesForDigest(coachInfo?.name || coach.name, windowStart, windowEnd);

    if (!digest) {
      return { success: true, skipped: "No unread messages" };
    }

    const subject = generateCoachDigestSubject(digest);
    const body = generateCoachDigestEmailBody(digest, getLocalDateString(timezone));

    await sendSimpleEmail(coach.email, subject, body, "Awaken Admin");

    await recordCoachDigestSent(coach.name, today, digest.totalMessageCount, digest.userCount);
    return { success: true, emailSent: true };
  } catch (error) {
    console.error(`[COACH DIGEST] Error processing coach ${coach.name}`, error);
    return { success: false, error: (error as Error).message };
  }
};

/* -------------------------------------------------------------------------- */
/*                    Main entry – to be called by cron job                    */
/* -------------------------------------------------------------------------- */

export const processCoachDailyDigests = async (): Promise<void> => {
  try {
    console.log("[COACH DIGEST] Starting coach digest processing…");
    const coaches = await getCoachesForDigest();
    if (coaches.length === 0) {
      console.log("[COACH DIGEST] No coaches with emails");
      return;
    }

    console.log(`[COACH DIGEST] Found ${coaches.length} coaches with emails`);
    console.log(coaches);

    const results = await Promise.allSettled(coaches.map(processCoachDigest));

    let sent = 0,
      skipped = 0,
      errors = 0;
    results.forEach((result, index) => {
      const coachName = coaches[index].name;
      if (result.status === "fulfilled") {
        const { emailSent, skipped: skipReason, error } = result.value;
        if (emailSent) {
          sent++;
          console.log(`[COACH DIGEST] ✅ Successfully sent digest to ${coachName}`);
        } else if (skipReason) {
          skipped++;
          console.log(`[COACH DIGEST] ⏭️ Skipped coach ${coachName}: ${skipReason}`);
        } else if (error) {
          errors++;
          console.error(`[COACH DIGEST] ❗️ Handled error for coach ${coachName}: ${error}`);
        }
      } else {
        errors++;
        console.error(`[COACH DIGEST] ❌ Promise rejected for coach ${coachName}:`, result.reason);
      }
    });

    console.log(`[COACH DIGEST] Completed: ${sent} emails, ${skipped} skipped, ${errors} errors`);
  } catch (e) {
    console.error("[COACH DIGEST] Fatal error", e);
  }
};
