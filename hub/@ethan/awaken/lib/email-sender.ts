"use server";

/**
 * Simple email sending utilities for friend-like, minimally styled emails
 */

import Reframe from "@";
import { generateGiftEmailHtml } from "./email-templates.ts";

// Base URL for building links in emails. Falls back to production domain if not provided.
const APP_URL = Reframe.env.APP_URL ?? "https://awaken.is";

/**
 * Converts plain text to simple HTML, preserving line breaks and basic formatting
 * Supports **bold**, *italic*, and [link](url) markdown-style formatting
 */
function textToSimpleHtml(text: string): string {
  return text
    .split('\n\n')
    .filter(para => para.trim() !== '')
    .map(para => {
      const trimmedPara = para.trim();

      // Convert '---' to <hr>
      if (trimmedPara === '---') {
        return '<hr style="border: none; border-top: 1px solid #ddd; margin: 2em 0;">';
      }

      // Convert [text](url) to <a href="url">text</a>
      para = para.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" style="color: #0066cc; text-decoration: none;">$1</a>');
      // Convert **text** to <strong>text</strong>
      para = para.replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>');
      // Convert *text* to <em>text</em>
      para = para.replace(/\*([^*]+)\*/g, '<em>$1</em>');

      return `<p>${para.replace(/\n/g, '<br>')}</p>`;
    })
    .join('\n');
}

/**
 * Sends a simple, friend-like email without heavy styling
 * Perfect for organic emails that should feel personal and authentic
 * 
 * @param email - Recipient email address
 * @param subject - Email subject line
 * @param message - Plain text message (supports markdown: **bold**, *italic*, and [links](url))
 * @param fromName - Optional sender name (defaults to "Kokoro")
 * @returns Postmark API response
 */
export const sendSimpleEmail = async (
  email: string, 
  subject: string, 
  message: string, 
  fromName: string = "Kokoro",
  includeReplyLink: boolean = true
) => {
  try {
    console.log("[SIMPLE_EMAIL] Sending email to:", email);
    console.log("[SIMPLE_EMAIL] Subject:", subject);
    
    // Convert message to simple HTML
    const htmlContent = textToSimpleHtml(message);
    
    // Create minimal HTML email
    const emailHtml = `<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
</head>
<body style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif; color: #333; line-height: 1.8; font-size: 16px; max-width: 600px; margin: 0 auto; padding: 20px;">
  ${htmlContent}
  ${includeReplyLink ? `<p style="margin-top: 30px;">
    <a href="${APP_URL}/chat" style="color: #0066cc; text-decoration: underline;">Reply</a>
  </p>` : ''}
  <p style="margin-top: 50px; font-size: 12px; color: #666;">
    © 2025 Awaken. All rights reserved.<br>
    <a href="${APP_URL}/chat" style="color: #666;">Manage email settings in the menu</a>
  </p>
</body>
</html>`;

    const postmarkBody = {
      From: `${fromName} <<EMAIL>>`,
      To: email,
      Subject: subject,
      HtmlBody: emailHtml,
      TextBody: message, // Plain text fallback
      MessageStream: 'outbound'
    };

    console.log("[SIMPLE_EMAIL] Sending email via Postmark");

    const response = await fetch('https://api.postmarkapp.com/email', {
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'X-Postmark-Server-Token': Reframe.env.POSTMARK_API_TOKEN
      },
      body: JSON.stringify(postmarkBody)
    });

    const result = await response.json();
    console.log("[SIMPLE_EMAIL] Email sent successfully:", result);
    return result;
  } catch (error) {
    console.error("[SIMPLE_EMAIL] Error sending email:", error);
    throw error;
  }
};

/**
 * Sends a gift essence notification email
 * @param recipientEmail - Recipient's email address
 * @param senderName - Name of the person sending the gift
 * @param essenceAmount - Amount of essence gifted
 * @param message - Optional personal message
 * @param code - Gift code for redemption
 * @param isAutoCredited - Whether the gift was automatically credited
 * @returns Postmark API response
 */
export const sendGiftEmail = async (
  recipientEmail: string,
  senderName: string,
  essenceAmount: number,
  message?: string,
  code?: string,
  isAutoCredited: boolean = false
) => {
  try {
    console.log("[GIFT_EMAIL] Sending gift email to:", recipientEmail);
    
    const subject = isAutoCredited 
      ? `${senderName} gifted you ${essenceAmount} Awaken Essence!` 
      : `${senderName} gifted you an experience with Awaken!`;
    
    // Generate the styled email HTML
    const emailHtml = generateGiftEmailHtml(
      recipientEmail,
      senderName,
      essenceAmount,
      message,
      code,
      isAutoCredited
    );
    
    // Create plain text version for fallback
    let plainTextMessage = "";
    
    if (isAutoCredited) {
      plainTextMessage = `Great news! ${senderName} has sent you a gift of ${essenceAmount} Essence for Awaken.\n\nWe've already added it to your account balance, so you can start using it right away.`;
      
      if (message) {
        plainTextMessage += `\n\n${senderName} included this message for you:\n\n"${message}"`;
      }
      
      plainTextMessage += `\n\n---\n\nContinue your journey: ${APP_URL}/chat`;
    } else {
      plainTextMessage = `${senderName} has sent you a special gift!\n\nYou've received ${essenceAmount} Essence to start your transformative journey with Awaken.`;
      
      if (message) {
        plainTextMessage += `\n\n${senderName} says:\n\n"${message}"`;
      }
      
      plainTextMessage += `\n\n---\n\nRedeem your gift: ${APP_URL}/redeem?g=${code}\n\nOr use this code: ${code}\n\n---\n\nWhat is Awaken?\nAwaken AI walks with you 24/7, guiding you to love, presence, and inner power in the raw moments of daily life.`;
    }
    
    const postmarkBody = {
      From: `Awaken <<EMAIL>>`,
      To: recipientEmail,
      Subject: subject,
      HtmlBody: emailHtml,
      TextBody: plainTextMessage, // Plain text fallback
      MessageStream: 'outbound'
    };

    console.log("[GIFT_EMAIL] Sending email via Postmark");

    const response = await fetch('https://api.postmarkapp.com/email', {
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'X-Postmark-Server-Token': Reframe.env.POSTMARK_API_TOKEN
      },
      body: JSON.stringify(postmarkBody)
    });

    const result = await response.json();
    console.log("[GIFT_EMAIL] Email sent successfully:", result);
    return result;
  } catch (error) {
    console.error("[GIFT_EMAIL] Error sending gift email:", error);
    throw error;
  }
};