import { db } from "./db.ts";
import { sql } from "npm:kysely";

// Type definitions (using generated DB types)
export type EmailJob = {
  id: string;
  userId: string;
  coachId: string;
  stage: 1 | 2 | 3;
  type: "acknowledgement" | "quick_reflection" | "resource";
  sendAt: string; // Dates are stored as strings in the DB
  sentAt: string | null;
  cancelled: number; // 0 = false, 1 = true (SQLite boolean)
  createdAt: string; // Dates are stored as strings in the DB
};

// Lightweight Data Access Object for emailJob table
export const EmailJobDAO = {
  findPending: (uid: string) =>
    db
      .selectFrom("emailJob")
      .selectAll()
      .where("userId", "=", uid)
      .where("sentAt", "is", null)
      .where("cancelled", "=", 0)
      .executeTakeFirst(),

  cancelPending: (uid: string) =>
    db
      .updateTable("emailJob")
      .set({ cancelled: 1 })
      .where("userId", "=", uid)
      .where("sentAt", "is", null)
      .where("cancelled", "=", 0)
      .execute(),

  insert: (row: Omit<EmailJob, "sentAt" | "cancelled">) =>
    db
      .insertInto("emailJob")
      .values({
        ...row,
        sentAt: null,
        cancelled: 0
      })
      .execute(),
};

/**
 * Mark a specific email job as cancelled
 * @param jobId - The ID of the job to cancel
 */
export async function markJobCancelled(jobId: string): Promise<void> {
  await db
    .updateTable("emailJob")
    .set({ cancelled: 1 })
    .where("id", "=", jobId)
    .where("cancelled", "=", 0)
    .execute();
}