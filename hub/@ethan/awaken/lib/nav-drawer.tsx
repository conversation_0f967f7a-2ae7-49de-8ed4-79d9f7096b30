"use client";

import React, { useRef } from "npm:react@canary";
import { Button } from "@reframe/ui/main.tsx";
import { motion, AnimatePresence } from "npm:framer-motion";
import { Logo } from "./logo.tsx";
import {
  SparklesIcon,
  LogOutIcon,
  MessageIcon,
  UserIcon,
  StarIcon,
  SmallBellIcon,
  EssenceIcon,
  GiftIcon
} from "./icons.tsx";

interface NavDrawerProps {
  user: any;
  isSubscribed: boolean;
  isOpen: boolean;
  onProfileClick: () => void;
  onUpgradeClick: () => void;
  onSignOut: () => void;
  onClose: () => void;
  onGiftClick?: () => void; // Add gift click handler
  currentPath?: string; // Add current path prop to track active page
  essenceBalance?: number; // Add essence balance prop
  showGiftOption?: boolean; // Feature flag for gift option
}

export const NavDrawer = ({
  user,
  isSubscribed,
  isOpen,
  onProfileClick,
  onUpgradeClick,
  onSignOut,
  onClose,
  onGiftClick,
  currentPath = '', // Default to empty string
  essenceBalance = 0, // Default to 0 if not provided
  showGiftOption = true // Default to true (can be controlled by feature flag)
}: NavDrawerProps) => {
  const handleTalkToTeam = () => {
    globalThis.location.href = "mailto:<EMAIL>?subject=Thoughts%20on%20Awaken";
  };

  const handleNavigate = (url: string) => {
    onClose();
    setTimeout(() => {
      globalThis.location.href = url;
    }, 300);
  };

  // Helper to check if menu item is active
  const isActive = (path: string) => {
    return currentPath.includes(path);
  };

  const backdropTouchStartXRef = useRef<number | null>(null);
  const backdropTouchEndXRef = useRef<number | null>(null);
  const SWIPE_THRESHOLD = 50;

  const menuVariants = {
    hidden: { x: "-100%", transition: { type: "tween", duration: 0.2, ease: "easeOut" } },
    visible: { x: 0, transition: { type: "tween", duration: 0.2, ease: "easeOut", staggerChildren: 0.01, when: "beforeChildren" } }
  };

  const itemVariants = {
    hidden: { opacity: 0, x: -5 },
    visible: { opacity: 1, x: 0, transition: { duration: 0.1 } },
    exit: { opacity: 0, x: -5, transition: { duration: 0 } }
  };

  const backdropVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.2 } },
    exit: { opacity: 0, transition: { duration: 0.2, delay: 0 } }
  };

  const Separator = () => (
    <motion.div variants={itemVariants} initial="hidden" animate="visible" exit="exit" className="h-px bg-[#FCA31133] mx-4 my-1" />
  );

  const handleBackdropTouchStart = (e: React.TouchEvent<HTMLDivElement>) => {
    e.stopPropagation();
    if (e.touches.length !== 1) { backdropTouchStartXRef.current = null; return; }
    backdropTouchEndXRef.current = null;
    backdropTouchStartXRef.current = e.touches[0].clientX;
  };

  const handleBackdropTouchMove = (e: React.TouchEvent<HTMLDivElement>) => {
    e.stopPropagation();
    if (e.touches.length !== 1) { backdropTouchStartXRef.current = null; return; }
    if (backdropTouchStartXRef.current === null) return;
    backdropTouchEndXRef.current = e.touches[0].clientX;
  };

  const handleBackdropTouchEnd = (e: React.TouchEvent<HTMLDivElement>) => {
    e.stopPropagation();
    if (backdropTouchStartXRef.current === null || backdropTouchEndXRef.current === null) {
      backdropTouchStartXRef.current = null;
      return;
    }
    const deltaX = backdropTouchEndXRef.current - backdropTouchStartXRef.current;
    if (deltaX < -SWIPE_THRESHOLD) onClose();
    backdropTouchStartXRef.current = null;
    backdropTouchEndXRef.current = null;
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          <motion.div
            className="fixed inset-0 bg-black bg-opacity-50 z-40"
            initial="hidden"
            animate="visible"
            exit="exit"
            variants={backdropVariants}
            onClick={onClose}
            onTouchStart={handleBackdropTouchStart}
            onTouchMove={handleBackdropTouchMove}
            onTouchEnd={handleBackdropTouchEnd}
            style={{ pointerEvents: "auto", touchAction: "none" }}
          />
          <motion.div
            className="fixed left-0 top-0 h-full w-64 sm:w-72 bg-[#111111] border-r border-[#FCA31133] z-50 flex flex-col overflow-y-auto"
            onTouchStart={(e) => e.stopPropagation()}
            onTouchMove={(e) => e.stopPropagation()}
            onTouchEnd={(e) => e.stopPropagation()}
            initial="hidden"
            animate="visible"
            exit="hidden"
            variants={menuVariants}
            drag="x"
            dragConstraints={{ left: 0, right: 0 }}
            dragElastic={0.1}
            onDragEnd={(event, info) => { if (info.offset.x < -80 || info.velocity.x < -300) onClose(); }}
          >
            <div className="p-4 flex-shrink-0 h-[60px] flex items-center justify-between">
              <div className="flex items-center gap-2 pl-1">
                <Logo size={20} />
                <span className="text-base font-normal text-[#FCA311] pl-1">awaken</span>
              </div>
              <Button variant="ghost" onClick={onClose} className="text-gray-400 hover:text-white p-1 rounded-md" aria-label="Close menu">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>
              </Button>
            </div>
            <motion.div className="flex flex-col flex-grow p-3 sm:p-4 justify-start pt-2 pb-5" initial="hidden" animate="visible" exit="exit" variants={{ visible: { transition: { staggerChildren: 0.01 } }, hidden: { transition: { staggerChildren: 0.01, staggerDirection: -1 } } }}>
              <motion.button 
                variants={itemVariants} 
                onClick={onProfileClick} 
                className={`flex w-full px-4 py-3 text-base ${isActive('account') ? 'text-[#FCA311] bg-[#FCA31133]' : 'text-white hover:bg-[#FCA31133]'} items-center gap-4 rounded-md`}
              >
                <UserIcon className="w-6 h-6" /> My Account
              </motion.button>
              <Separator />
              <motion.button 
                variants={itemVariants} 
                onClick={() => handleNavigate("/")} 
                className={`flex w-full px-4 py-3 text-base ${isActive('chat') || currentPath === '/' ? 'text-[#FCA311] bg-[#FCA31133]' : 'text-white hover:bg-[#FCA31133]'} items-center gap-4 rounded-md`}
              >
                <MessageIcon className="w-6 h-6" /> Chat
              </motion.button>
              <Separator />
              <motion.button 
                variants={itemVariants} 
                onClick={() => handleNavigate("/starred")} 
                className={`flex w-full px-4 py-3 text-base ${isActive('integrate') ? 'text-[#FCA311] bg-[#FCA31133]' : 'text-white hover:bg-[#FCA31133]'} items-center gap-4 rounded-md`}
              >
                <StarIcon className="w-6 h-6" /> Starred Messages
              </motion.button>
              {showGiftOption && onGiftClick && (
                <>
                  <Separator />
                  <motion.button 
                    variants={itemVariants} 
                    onClick={() => { onGiftClick(); onClose(); }} 
                    className="flex w-full px-4 py-3 text-base text-white hover:bg-[#FCA31133] items-center gap-4 rounded-md"
                  >
                    <GiftIcon className="w-6 h-6" /> Gift Awaken
                  </motion.button>
                </>
              )}
              {!isSubscribed && (
                <>
                  <Separator />
                  <motion.button variants={itemVariants} onClick={() => { onUpgradeClick(); onClose(); }} className="flex w-full px-4 py-3 text-base text-white hover:bg-[#FCA31133] items-center gap-4 rounded-md">
                    <SparklesIcon className="w-6 h-6" /> Upgrade
                  </motion.button>
                </>
              )}
              <Separator />
              <motion.button variants={itemVariants} onClick={() => { handleTalkToTeam(); onClose(); }} className="flex w-full px-4 py-3 text-base text-gray-300 hover:bg-[#FCA31133] items-center gap-4 rounded-md">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="w-6 h-6"><path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path><polyline points="22,6 12,13 2,6"></polyline></svg> Email Team
              </motion.button>
              <Separator />
              <motion.button 
                variants={itemVariants} 
                onClick={() => handleNavigate("/changelog")} 
                className={`flex w-full px-4 py-3 text-base ${isActive('changelog') ? 'text-[#FCA311] bg-[#FCA31133]' : 'text-gray-300 hover:bg-[#FCA31133]'} items-center gap-4 rounded-md`}
              >
                <SmallBellIcon className="w-6 h-6" /> Updates
              </motion.button>
              
              {/* Essence Balance Display */}
              <div className="mt-auto pt-4">
                <motion.button 
                  variants={itemVariants} 
                  initial="hidden" 
                  animate="visible" 
                  exit="exit" 
                  onClick={onProfileClick}
                  className="flex w-full px-4 py-3 text-base text-[#FCA311] hover:bg-[#FCA31133] items-center gap-4 rounded-md"
                >
                  <EssenceIcon size={24} className="w-6 h-6" style={{ color: "#ff6b35" }} />
                  <span className="text-sm">{essenceBalance.toFixed(1)} essence</span>
                </motion.button>
              </div>
            </motion.div>
            <div className="p-2 sm:p-4 border-t border-[#FCA31133] flex-shrink-0">
                <motion.button variants={itemVariants} initial="hidden" animate="visible" exit="exit" onClick={() => { onSignOut(); }} className="flex w-full px-4 py-3 text-base text-gray-300 hover:bg-[#FCA31133] items-center gap-4 rounded-md">
                  <LogOutIcon className="w-6 h-6" /> Sign out
                </motion.button>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
}
