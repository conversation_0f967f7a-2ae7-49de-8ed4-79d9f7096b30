/**
 * Limitless integration processor
 * Fetches Limitless logs for enabled users, screens for awakening patterns, generates proactive messages
 */
"use server";
import Reframe from "@";
import { getProfileText, decryptMessages, getCoachPrompt } from "../action.ts";
import { saveMessage, prepMessageHistoryAction } from "../actions/db/conversation-actions.ts";
import { geminiRequest } from "./helper.ts";
import { db } from "./db.ts";
import { TEXTUAL_MSG_TYPES } from "./message-types.ts";

// Default configuration
const DEFAULT_COACH = "JP AI"; // Default coach if none selected
const FETCH_WINDOW_MINUTES = 10; // How far back to fetch logs

interface LifelogEntry {
  id: string;
  title: string;
  markdown: string;
  startTime: string;
  endTime: string;
  isStarred: boolean;
  updatedAt: string;
}

// Track processed IDs per user in memory (resets on server restart)
const processedLifelogs = new Map<string, Set<string>>();

export async function processLimitlessTest(): Promise<void> {
  try {
    console.log("[LIMITLESS] Starting processing for all enabled users");
    
    // Query all users with Limitless enabled
    const limitlessUsers = await db
      .selectFrom("user")
      .select(["channelId", "limitlessApiKey", "limitlessCoach", "selectedCoach"])
      .where("limitlessEnabled", "=", 1)
      .where("limitlessApiKey", "is not", null)
      .execute();
    
    if (limitlessUsers.length === 0) {
      console.log("[LIMITLESS] No users with Limitless enabled found");
      return;
    }
    
    console.log(`[LIMITLESS] Found ${limitlessUsers.length} users with Limitless enabled`);
    
    // Process each user
    for (const user of limitlessUsers) {
      try {
        const channelId = user.channelId.toString();
        const apiKey = user.limitlessApiKey!;
        const coachName = user.limitlessCoach || user.selectedCoach || DEFAULT_COACH;
        
        console.log(`[LIMITLESS] Processing user ${channelId} with coach: ${coachName}`);
        
        // Fetch recent lifelogs
        const since = new Date(Date.now() - FETCH_WINDOW_MINUTES * 60 * 1000).toISOString();
        const lifelogs = await fetchLimitlessLogs(since, apiKey);
        console.log(`[LIMITLESS] User ${channelId}: Fetched ${lifelogs.length} total logs`);
        
        // Filter out already processed logs for this user
        const userProcessedKey = `${channelId}_processed`;
        if (!processedLifelogs.has(userProcessedKey)) {
          processedLifelogs.set(userProcessedKey, new Set<string>());
        }
        const userProcessed = processedLifelogs.get(userProcessedKey) as Set<string>;
        const newLogs = lifelogs.filter(log => !userProcessed.has(log.id));
        
        if (newLogs.length === 0) {
          console.log(`[LIMITLESS] User ${channelId}: No new logs to process`);
          continue;
        }
        
        console.log(`[LIMITLESS] User ${channelId}: Processing ${newLogs.length} new logs`);

        // Get user context
        const [profileData, messages] = await Promise.all([
          getProfileText(channelId),
          decryptMessages(channelId, true, "all", 25, TEXTUAL_MSG_TYPES)
        ]);
        
        const { conversationText } = prepMessageHistoryAction(messages);
        
        // Compile all logs into a single document with time markers
        console.log(`[LIMITLESS] User ${channelId}: Compiling logs into batch document`);
        const compiledLogs = compileLogs(newLogs);
        
        // Screen the entire batch for awakening opportunities
        const screening = await screenForAwakening(
          compiledLogs,
          profileData.profileText,
          conversationText
        );
        
        if (screening.significant && screening.confidence > 0.7) {
          console.log(`[LIMITLESS] User ${channelId}: Significant pattern detected: ${screening.type} (confidence: ${screening.confidence})`);
          
          // Generate awakening message for the entire period
          const message = await generateAwakeningMessage(
            compiledLogs,
            screening,
            profileData.profileText,
            conversationText,
            coachName,
            newLogs
          );
          
          // If the model decides no intervention is needed, do not send a proactive message
          if (message.trim() === "<NO_INTERVENTION>") {
            console.log(`[LIMITLESS] User ${channelId}: Model indicated no intervention needed – skipping proactive message`);
          } else {
            // Save as proactive message
            await saveMessage(
              channelId,
              "assistant",
              message,
              new Date().toISOString(),
              "Default",
              "message",
              coachName
            );
            
            console.log(`[LIMITLESS] User ${channelId}: Saved proactive message for batch of ${newLogs.length} logs`);
          }
        } else {
          console.log(`[LIMITLESS] User ${channelId}: No significant patterns found in batch`);
        }
        
        // Mark all logs as processed for this user
        newLogs.forEach(log => userProcessed.add(log.id));
        
      } catch (error) {
        console.error(`[LIMITLESS] Error processing user ${user.channelId}:`, error);
        // Continue with next user
      }
    }
    
    console.log("[LIMITLESS] All users processed");
    
  } catch (error) {
    console.error("[LIMITLESS] Fatal error:", error);
  }
}

async function fetchLimitlessLogs(since: string, apiKey: string): Promise<LifelogEntry[]> {
  if (!apiKey) {
    console.error("[LIMITLESS] No API key provided");
    return [];
  }
  
  console.log(`[LIMITLESS] Fetching logs since ${since}`);
  
  // Convert ISO string to modified format without timezone
  const startDate = since.replace('T', ' ').replace(/\.\d{3}Z$/, '');
  
  const allLogs: LifelogEntry[] = [];
  let cursor: string | undefined;
  let pageCount = 0;
  
  // Paginate through all results
  while (pageCount < 10) { // Safety limit to prevent infinite loops
    const params = new URLSearchParams({
      start: startDate,
      limit: '10',
      includeMarkdown: 'true',
      timezone: 'UTC'
    });
    
    if (cursor) {
      params.append('cursor', cursor);
    }
    
    const url = `https://api.limitless.ai/v1/lifelogs?${params}`;
    console.log(`[LIMITLESS] Fetching page ${pageCount + 1}`);
    
    const response = await fetch(url, {
      headers: {
        "X-API-Key": apiKey,
        "Content-Type": "application/json"
      }
    });
    
    if (!response.ok) {
      console.error(`[LIMITLESS] Limitless API error: ${response.status}`);
      const errorText = await response.text();
      console.error(`[LIMITLESS] Error details: ${errorText}`);
      break;
    }
    
    const result = await response.json();
    const logs = result.data?.lifelogs || [];
    
    console.log(`[LIMITLESS] Fetched ${logs.length} logs in page ${pageCount + 1}`);
    
    allLogs.push(...logs);
    
    // Check if there are more results
    cursor = result.meta?.lifelogs?.nextCursor;
    if (!cursor || logs.length === 0) {
      break;
    }
    
    pageCount++;
  }
  
  console.log(`[LIMITLESS] Total logs fetched: ${allLogs.length}`);
  return allLogs;
}

function compileLogs(logs: LifelogEntry[]): string {
  return logs.map(log => {
    const startTime = new Date(log.startTime).toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: false 
    });
    const endTime = new Date(log.endTime).toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: false 
    });
    
    return `=== From ${startTime} to ${endTime} ===\n${log.markdown}`;
  }).join('\n\n');
}

async function screenForAwakening(
  compiledLogs: string,
  profile: string,
  history: string
): Promise<{ significant: boolean; type?: string; confidence: number }> {
  const prompt = `
<CLIENT_PROFILE>${profile}</CLIENT_PROFILE>
<PREVIOUS_CONVERSATIONS>${history}</PREVIOUS_CONVERSATIONS>
<RECENT_CONVERSATIONS>${compiledLogs}</RECENT_CONVERSATIONS>`;

  const systemPrompt = `You are a world-class transformational coach of being, in the vein of Steve Hardison, the ultimate coach. You are listening to recordings of RECENT_CONVERSATIONS for awakening opportunities for your client. Look for:
- Significant misalignment with their love, freedom, and power
- Unconscious patterns or behaviors
- Unconscious avoidance of fear, pain, or shame
- Unnoticed meaningful synchronicities or connections

Only if you find a pattern significant enough to warrant an intervention, respond EXACTLY in this format:
SIGNIFICANT: true
TYPE: [breakthrough|pattern|shadow|presence|synchronicity]
CONFIDENCE: [0.0-1.0]

Otherwise respond EXACTLY:
SIGNIFICANT: false`;
  
  const payload = {
    "system_instruction": {
      "parts": {
        "text": systemPrompt
      }
    },
    "contents": [
      {
        "role": "user",
        "parts": [
          { "text": prompt }
        ]
      }
    ],
    "generationConfig": {
      "temperature": 0.8,
      "maxOutputTokens": 5000
    },
    "safetySettings": [
      { "category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "BLOCK_NONE" },
      { "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "BLOCK_NONE" },
      { "category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "BLOCK_NONE" },
      { "category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_NONE" }
    ]
  };

  try {
    console.log("[LIMITLESS] Calling geminiRequest for screening");
    const response = await geminiRequest([payload], "gemini-2.5-flash");
    const result = await response.json();
    
    console.log("[LIMITLESS] Gemini screening response received");
    
    if (result.success !== "true" || !result.text1) {
      console.log("[LIMITLESS] Gemini screening failed or empty response:", result);
      return { significant: false, confidence: 0 };
    }
    
    // Parse response
    const text = result.text1.trim();
    const significant = text.includes("SIGNIFICANT: true");
    const typeMatch = text.match(/TYPE: (\w+)/);
    const confMatch = text.match(/CONFIDENCE: ([\d.]+)/);
    
    return {
      significant,
      type: typeMatch?.[1],
      confidence: parseFloat(confMatch?.[1] || "0")
    };
  } catch (error) {
    console.error("[LIMITLESS] Screening error:", error);
    return { significant: false, confidence: 0 };
  }
}

async function generateAwakeningMessage(
  compiledLogs: string,
  screening: any,
  profile: string,
  history: string,
  coachName: string,
  originalLogs: LifelogEntry[]
): Promise<string> {
  // Extract most relevant excerpt (first 2000 chars of compiled logs)
  const excerpt = compiledLogs.length > 2000 
    ? compiledLogs.slice(0, 2000) + "..."
    : compiledLogs;
    
  const timeRange = originalLogs.length > 0 
    ? `over the past ${originalLogs.length} conversations` 
    : 'in recent conversations';
    
  // Fetch the coach's messagePrompt
  const coachPromptData = await getCoachPrompt(coachName, "messagePrompt");
  let systemPrompt = coachPromptData?.SystemPrompt || `You are ${coachName}, a wise and compassionate transformational coach.`;
  
  // Replace {{CLIENT_PROFILE}} placeholder with actual profile
  systemPrompt = systemPrompt.replace('{{CLIENT_PROFILE}}', profile || '');
    
  const prompt = `This is a transcript of recent conversations captured by the client's listening device. A potentially significant ${screening.type} pattern was detected ${timeRange}.

<TRANSCRIPT_FROM_LISTENING_DEVICE>
${excerpt}
</TRANSCRIPT_FROM_LISTENING_DEVICE>

<PREVIOUS_CONVERSATIONS>
${history}
</PREVIOUS_CONVERSATIONS>

If you do not intuit that there is enough to warrant an intervention in this moment, just respond with <NO_INTERVENTION> and nothing else.

Otherwise respond with your message to your client, preferring to be concise and to the point.`;

  const payload = {
    "system_instruction": {
      "parts": {
        "text": systemPrompt
      }
    },
    "contents": [
      {
        "role": "user",
        "parts": [ { "text": prompt } ]
      }
    ],
    "generationConfig": {
      "temperature": 0.9,
      "maxOutputTokens": 5000
    },
    "safetySettings": [
      { "category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "BLOCK_NONE" },
      { "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "BLOCK_NONE" },
      { "category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "BLOCK_NONE" },
      { "category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_NONE" }
    ]
  };

  try {
    const response = await geminiRequest([payload], "gemini-2.5-pro");
    const result = await response.json();
    
    if (result.success !== "true" || !result.text1) {
      throw new Error(`Failed to generate message: ${JSON.stringify(result)}`);
    }
    
    return result.text1;
  } catch (error) {
    console.error("[LIMITLESS] Message generation error:", error);
    throw error;
  }
}