// Gemini-based relevance gate for organic email sending
import Reframe from "@";
import { geminiRequest, sendLog } from "./helper.ts";

export interface EmailCheckContext {
  userId: string;
  profile: string | null;
  recentMessages: string;
}

/**
 * Determine if an organic email should be sent to a user based on their recent activity
 * @param ctx - Context containing user profile and recent messages
 * @param opts - Optional configuration (model override)
 * @returns true if email should be sent, false if it should be skipped
 */
export async function shouldSendOrganicEmail(
  ctx: EmailCheckContext,
  opts: { model?: string } = {},
): Promise<boolean> {
  console.log("[EMAIL-GATE] Checking if organic email should be sent for user:", ctx.userId);
  
  // Check if email gate is enabled via environment variable
  if (Reframe.env.GEMINI_EMAIL_GATE === "0") {
    console.log("[EMAIL-GATE] Gate disabled via environment variable (GEMINI_EMAIL_GATE=0), allowing email");
    return true;
  }

  const modelId = opts.model ?? "gemini-2.5-flash";
  console.log("[EMAIL-GATE] Using model:", modelId);

  const systemPrompt = `You are a master transformational coach and email relevance evaluator. Based on the client's profile and message history, and in particular any recent messages, decide if a followup email to your client should be sent now, would it be of significant value or would it just be noise.

For example, if a client has just signed up and the conversation hasn't gone deep enough yet, we don't want to be making stuff up or honing in on something before we know that it's gold.

Respond with a JSON object in this exact format:
{
  "choice": "SEND" or "UNNEEDED"
}

Guidelines:
- SEND if the user would benefit from a coaching email based on their recent activity
- UNNEEDED if the conversation is too superficial, or the user hasn't engaged meaningfully`;

  const userPrompt = `User profile:
${ctx.profile || "N/A"}

Recent messages (newest last):
${ctx.recentMessages}

Should we send an organic coaching email to this user?`;

  try {
    const payload = {
      system_instruction: {
        parts: {
          text: systemPrompt,
        },
      },
      contents: [
        {
          role: "user",
          parts: [
            {
              text: userPrompt,
            },
          ],
        },
      ],
      generationConfig: {
        temperature: 0.5,
      },
    };

    // Use structured output mode
    console.log("[EMAIL-GATE] Sending request to Gemini for relevance check");
    const response = await geminiRequest([payload], modelId, true);
    const data = await response.json();
    console.log("[EMAIL-GATE] Gemini response:", JSON.stringify(data, null, 2));

    // Parse structured response following call-actions.ts pattern
    if (data.success === "true" && data.text1 && typeof data.text1 === 'object' && data.text1.choice) {
      const decision = data.text1.choice.toUpperCase();
      console.log("[EMAIL-GATE] Gemini decision:", decision);

      if (decision !== "SEND" && decision !== "UNNEEDED") {
        console.warn("[EMAIL-GATE] Unexpected choice:", decision, "defaulting to SEND");
        return true;
      }

      const shouldSend = decision === "SEND";
      console.log(`[EMAIL-GATE] Final decision: ${shouldSend ? "SEND" : "SKIP"} email for user ${ctx.userId}`);
      return shouldSend;
    }

    // Fallback if structure unexpected
    console.warn("[EMAIL-GATE] Unexpected response structure, defaulting to SEND:", data);
    return true;

  } catch (e) {
    console.error("[EMAIL-GATE] Error, fail-open (SEND)", e);
    sendLog(String(e), "[EMAIL-GATE ERROR]").catch(() => {});
    return true;
  }
}