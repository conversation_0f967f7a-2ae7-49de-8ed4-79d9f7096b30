/**
 * Platform detection utilities for Awaken app
 */

/**
 * Detects if the current environment is iOS
 */
export const isIOS = (): boolean => {
  if (typeof window === 'undefined') return false;
  
  const userAgent = window.navigator.userAgent;
  return /iPad|iPhone|iPod/.test(userAgent);
};

/**
 * Detects if the current environment is running inside Expo
 */
export const isExpo = (): boolean => {
  if (typeof window === 'undefined') return false;
  
  // Check for Expo global objects that we'll inject
  if ((window as any).expo || (window as any).ExpoConstants || (window as any).__EXPO_ENV__) {
    return true;
  }
  
  // Check user agent for Expo
  const userAgent = window.navigator.userAgent;
  if (userAgent.includes('Expo')) {
    return true;
  }
  
  // Check for React Native WebView
  if ((window as any).ReactNativeWebView) {
    return true;
  }
  
  // Check for Expo-specific APIs
  try {
    // If expo-constants is available, we're likely in Expo
    if ((window as any).Constants || (globalThis as any).Constants) {
      return true;
    }
  } catch (e) {
    // Ignore errors
  }
  
  return false;
};

/**
 * Detects if the current environment is Expo running on iOS specifically
 */
export const isExpoIOS = (): boolean => {
  return isIOS() && isExpo();
};

/**
 * Detects if the current environment is a mobile device
 */
export const isMobile = (): boolean => {
  if (typeof window === 'undefined') return false;
  
  const userAgent = window.navigator.userAgent;
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
}; 