"use server";

import { createClient } from "../db/client.ts";
import { DB, Subscription, User } from "../db/types.ts";
import { PLANS } from "./plans.ts";
import { sql } from "npm:kysely";
import { getActionCost } from "./user-actions.ts";
import { verifyUserPermission } from "./auth-helper.ts";
import { sendLog } from "./helper.ts";

// Create a single db client instance to be used throughout the app
export const db = createClient();

// Export type for use in other files
export type DbClient = typeof db;

export const resetDailyCountsIfNeeded = async (channelId: string) => {
    const user = await db
        .selectFrom("user")
        .select(["lastDailyReset"])
        .where("channelId", "=", channelId)
        .executeTakeFirst();

    const now = new Date();
    const lastReset = user?.lastDailyReset ? new Date(user.lastDailyReset) : null;
    
    // Reset if last reset was on a different day or doesn't exist
    if (!lastReset || lastReset.getDate() !== now.getDate()) {
        await db
            .updateTable("user")
            .set({
                callCountToday: 0,
                callMinutesToday: 0,
                messageCountToday: 0,
                lastDailyReset: now.toISOString()
            })
            .where("channelId", "=", channelId)
            .execute();
    }
};

export const resetMonthlyCountsIfNeeded = async (channelId: string) => {
    const user = await db
        .selectFrom("user")
        .select(["lastMonthlyReset"])
        .where("channelId", "=", channelId)
        .executeTakeFirst();

    const subscription = await getSubscription(channelId);

    const now = new Date();
    const lastReset = user?.lastMonthlyReset ? new Date(user.lastMonthlyReset) : null;

    let doReset = !lastReset;
    if (lastReset) {
        const lastMonth = lastReset.getMonth();
        const currentMonth = now.getMonth();
        
        const msPerDay = 1000 * 60 * 60 * 24;
        const daysPassed = (now.getTime() - lastReset.getTime()) / msPerDay;

        if (daysPassed >= 30) {
            doReset = true;
        }
    }

    if (doReset) {
        await db
            .updateTable("user")
            .set({
                callMinsThisPeriod: 0,
                callMinutesToday: 0,
                callCountToday: 0,
                messagesThisPeriod: 0,
                messageCountToday: 0,
                lastDailyReset: now.toISOString(),
                lastMonthlyReset: now.toISOString(),
                monthlyEssenceBalance: PLANS[subscription.planId.split("_")[0]].essencePerMonth
            })
            .where("channelId", "=", channelId)
            .execute();
    }
};

export const resetUserDataIfNeeded = async (channelId: string) => {
    await Promise.all([
        resetDailyCountsIfNeeded(channelId),
        resetMonthlyCountsIfNeeded(channelId)
    ]);
};

export const getTotalEssenceBalance = async (channelId: string) => {
    await resetUserDataIfNeeded(channelId);
    const user = await db.selectFrom("user").select(["monthlyEssenceBalance", "addedEssenceBalance"]).where("channelId", "=", channelId).executeTakeFirst();
    const monthlyEssenceBalance = user?.monthlyEssenceBalance || 0;
    const addedEssenceBalance = user?.addedEssenceBalance || 0;
    return monthlyEssenceBalance + addedEssenceBalance;
};

export const deductEssenceBalance = async (channelId: string, amount: number) => {
    // first get monthly essence balance, and added essence balance
    const user = await db.selectFrom("user").select(["monthlyEssenceBalance", "addedEssenceBalance"]).where("channelId", "=", channelId).executeTakeFirst();
    
    const monthlyEssenceBalance = user?.monthlyEssenceBalance || 0;
    const addedEssenceBalance = user?.addedEssenceBalance || 0;

    // assert that the total essence balance is greater than the amount
    const totalEssenceBalance = monthlyEssenceBalance + addedEssenceBalance;
    if (totalEssenceBalance < amount - .50) {
        console.log("[SERVER] Essence balance lesser than amount");
        const message = `Total Essence Balance: ${totalEssenceBalance}, Amount: ${amount}`;
        sendLog(message, "[BAD CALL] Essence balance lesser than amount");
    }

    const effectiveAmount = Math.min(amount, totalEssenceBalance);

    // deduct the amount from the added essence balance
    const cutAmount = Math.min(effectiveAmount, monthlyEssenceBalance);
    const newMonthlyEssenceBalance = Math.max(0, monthlyEssenceBalance - cutAmount);
    const newAddedEssenceBalance = Math.max(0, addedEssenceBalance - (effectiveAmount - cutAmount));

    // deduct the amount from the added essence balance
    await db.updateTable("user").set({ monthlyEssenceBalance: newMonthlyEssenceBalance, addedEssenceBalance: newAddedEssenceBalance }).where("channelId", "=", channelId).execute();
};

export const incrementMessageCount = async (channelId: string, customEssenceCost?: number) => {
    try {
        await resetUserDataIfNeeded(channelId);
        await db.updateTable("user")
            .set({
                messagesThisPeriod: sql`messages_this_period + 1`,
                totalMessages: sql`total_messages + 1`,
                messageCountToday: sql`message_count_today + 1`,
            })
            .where("channelId", "=", channelId)
            .execute();

        const essenceToDeduct = customEssenceCost ?? getActionCost("message", 1);
        await deductEssenceBalance(channelId, essenceToDeduct);
    } catch (error) {
        console.error("Error incrementing message count", error);
    }
};

export const incrementCallMinutes = async (channelId: string, durationMinutes: number, customEssenceCost?: number) => {
    try {
        await resetUserDataIfNeeded(channelId);
        
        const roundedDuration = Math.round(durationMinutes * 10000) / 10000; // Rounds to 4 decimal places
        
        await db.updateTable("user")
            .set({
                callMinsThisPeriod: sql`ROUND(call_mins_this_period + ${roundedDuration}, 4)`,
                totalCallMins: sql`ROUND(total_call_mins + ${roundedDuration}, 4)`,
                callMinutesToday: sql`ROUND(call_minutes_today + ${roundedDuration}, 4)`,
                callCountToday: sql`call_count_today + 1`
            })
            .where("channelId", "=", channelId)
            .execute();

        // Use custom essence cost if provided, otherwise use default calculation
        const essenceToDeduct = customEssenceCost ?? getActionCost("call", roundedDuration);
        await deductEssenceBalance(channelId, essenceToDeduct);
    } catch (error) {
        console.error("Error incrementing call minutes", error);
    }
};


export interface UserData {
    messagesThisPeriod: number;
    callMinsThisPeriod: number;
    messageCountToday: number;
    callMinutesToday: number;
    callCountToday: number;
    totalMessages: number;
    totalCallMins: number;
    selectedCoach: string;
    emailDaily: boolean;
    subscription: Subscription | { planId: string };
    addedEssenceBalance: number;
    monthlyEssenceBalance: number;
    stripeSubscriptionId: string | null;
}

export const getUserData = async (channelId: string) => {
    if(!(await verifyUserPermission(channelId))) return null;
    try {
        // First, run the reset operations in parallel
        await resetUserDataIfNeeded(channelId);
        // Then, fetch user data and subscription in parallel
        const [userData, subscription] = await Promise.all([
            db.selectFrom("user")
              .select([
                "messagesThisPeriod", 
                "callMinsThisPeriod", 
                "messageCountToday", 
                "callMinutesToday", 
                "callCountToday", 
                "totalMessages", 
                "totalCallMins", 
                "selectedCoach", 
                "emailDaily", 
                "addedEssenceBalance", 
                "monthlyEssenceBalance",
                "stripeSubscriptionId"
              ])
              .where("channelId", "=", channelId)
              .executeTakeFirst(),
            getSubscription(channelId)
        ]);
        
        if(!userData) {
            throw new Error("No user data found for channelId: " + channelId);
        }

        // Combine the data
        const result: UserData = {
            ...userData,
            subscription
        };
        
        return result;
    } catch (error) {
        throw new Error("Error getting user data: " + error);
    }
};

export const getSubscription = async (channelId: string) => {
    try {
      console.log("[SUBSCRIPTION] Checking subscription for:", channelId);
      
      const user = await db
        .selectFrom("user")
        .select(["stripeSubscriptionId", "currentPlan"])
        .where("channelId", "=", Number(channelId))
        .executeTakeFirst();
  
      if (!user) {
        throw new Error("No user found for channelId: " + channelId);
      }
  
      if(!user.stripeSubscriptionId && user.currentPlan !== "free_plan") {
        return {
                planId: user.currentPlan,
            };
        }
      if(!user.stripeSubscriptionId) {
        return { planId: "free_plan" };
      }
  
      // Get active subscription details if exists
      const subscription: Subscription = user.stripeSubscriptionId ? 
        await db
          .selectFrom("subscription")
          .selectAll()
          .where("id", "=", user.stripeSubscriptionId)
          .where("status", "=", "active")
          .executeTakeFirst() 
        : null;
  
      // console.log("[SUBSCRIPTION] Subscription details:", subscription);
  
      return subscription
    } catch (error) {
      throw new Error("Error getting subscription: " + error);
    }
  }; 

/**
 * Checks if the last daily awakening was sent on a previous date
 * and resets it if needed.
 * 
 * @param channelId - The user's channel ID
 * @returns Boolean indicating if the date was reset
 */
export const resetDailyAwakeningDateIfNeeded = async (channelId: string) => {
    try {
        const user = await db
            .selectFrom("user")
            .select(["timezone", "lastDailyAwakeningDate"])
            .where("channelId", "=", channelId)
            .executeTakeFirst();
        
        // If no timezone or no last date, nothing to do
        if (!user?.timezone || !user?.lastDailyAwakeningDate) {
            return false;
        }
        
        // Get the current date in user's timezone
        const now = new Date();
        const userTime = new Date(now.toLocaleString("en-US", { timeZone: user.timezone }));
        const userDateStr = userTime.toISOString().split('T')[0];
        
        // If last awakening date is from a different day, reset it
        if (user.lastDailyAwakeningDate !== userDateStr) {
            console.log(`[DB] Resetting lastDailyAwakeningDate for user ${channelId} from ${user.lastDailyAwakeningDate} to null`);
            
            await db
                .updateTable("user")
                .set({ lastDailyAwakeningDate: null })
                .where("channelId", "=", channelId)
                .execute();
            
            return true;
        }
        
        return false;
    } catch (error) {
        console.error("Error resetting daily awakening date", error);
        return false;
    }
};

/**
 * Manually resets the lastDailyAwakeningDate for a user
 * Useful for testing daily awakening messages
 * 
 * @param channelId - The user's channel ID
 * @returns Boolean indicating if the reset was successful
 */
export const manuallyResetDailyAwakeningDate = async (channelId: string) => {
    try {
        console.log(`[DB] Manually resetting lastDailyAwakeningDate for user ${channelId}`);
        
        await db
            .updateTable("user")
            .set({ lastDailyAwakeningDate: null })
            .where("channelId", "=", channelId)
            .execute();
        
        return true;
    } catch (error) {
        console.error("Error manually resetting daily awakening date", error);
        return false;
    }
};

export const initiateUserProfile = async (channelId: string) => {
    try {
        await db
            .updateTable("user")
            .set({ profileInitiated: 1 })
            .where("channelId", "=", Number(channelId))
            .execute();
    } catch (error) {
        console.error("Error initiating user profile", error);
    }
};
    