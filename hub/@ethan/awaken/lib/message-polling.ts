"use client";

import { getMessagesSinceTimestamp } from "../action.ts";
import { UI_DEFAULT_TYPES } from "./message-types.ts";

export interface MessageType {
  Id: string;
  Date?: string;
  Sender: string;
  Type?: string;
  Content: string;
  CoachName?: string;
  Audio?: string | Uint8Array;
  Status?: string;
  isTemporary?: boolean;
  New?: boolean;
}

export interface PollingConfig {
  channelId: string | number;
  coachName?: string;
  intervalMs?: number;
  onNewMessages?: (messages: MessageType[]) => void;
  onError?: (error: Error) => void;
}

/**
 * Client-side sync time manager for incremental message polling
 */
export class SyncTimeManager {
  private static readonly STORAGE_KEY = 'awaken_last_sync_time';

  /**
   * Get the last sync time for a channel, or current time for first load
   */
  static getLastSyncTime(channelId: string | number): string {
    const key = `${this.STORAGE_KEY}_${channelId}`;
    const stored = localStorage.getItem(key);

    if (stored) {
      console.log(`[SyncTime] Retrieved last sync time for channel ${channelId}: ${stored}`);
      return stored;
    }

    // First time - use current time to avoid fetching all historical messages
    const currentTime = new Date().toISOString();
    console.log(`[SyncTime] First time sync for channel ${channelId}, using current time: ${currentTime}`);
    this.setLastSyncTime(channelId, currentTime);
    return currentTime;
  }

  /**
   * Update the last sync time for a channel
   */
  static setLastSyncTime(channelId: string | number, timestamp: string): void {
    const key = `${this.STORAGE_KEY}_${channelId}`;
    localStorage.setItem(key, timestamp);
    console.log(`[SyncTime] Updated last sync time for channel ${channelId}: ${timestamp}`);
  }

  /**
   * Clear sync time (useful for testing or reset)
   */
  static clearSyncTime(channelId: string | number): void {
    const key = `${this.STORAGE_KEY}_${channelId}`;
    localStorage.removeItem(key);
    console.log(`[SyncTime] Cleared sync time for channel ${channelId}`);
  }
}

export class MessagePollingService {
  private intervalId: number | null = null;
  private isPolling = false;
  private config: PollingConfig;
  private readonly DEFAULT_INTERVAL = 5000; // 5 seconds
  private recentlySentMessages: Set<string> = new Set(); // Track recently sent message content

  constructor(config: PollingConfig) {
    this.config = {
      intervalMs: this.DEFAULT_INTERVAL,
      ...config,
    };
  }

  /**
   * Start polling for new messages using incremental sync
   */
  start(): void {
    if (this.isPolling) {
      console.log("[MessagePolling] Already polling, ignoring start request");
      return;
    }

    console.log(`[MessagePolling] Starting incremental polling for channel ${this.config.channelId} every ${this.config.intervalMs}ms`);
    this.isPolling = true;

    // Poll immediately on start
    this.pollForNewMessages();

    // Set up interval for subsequent polls
    this.intervalId = globalThis.setInterval(() => {
      this.pollForNewMessages();
    }, this.config.intervalMs);
  }

  /**
   * Stop polling for new messages
   */
  stop(): void {
    if (!this.isPolling) {
      return;
    }

    console.log(`[MessagePolling] Stopping polling for channel ${this.config.channelId}`);
    this.isPolling = false;
    
    if (this.intervalId !== null) {
      globalThis.clearInterval(this.intervalId);
      this.intervalId = null;
    }
  }

  /**
   * Update the last sync time for this channel
   */
  updateLastSyncTime(timestamp: string): void {
    SyncTimeManager.setLastSyncTime(this.config.channelId, timestamp);
  }

  /**
   * Track a recently sent message to avoid duplicates from polling
   */
  trackRecentlySentMessage(content: string, messageId?: string): void {
    // Create a key that includes both content and timestamp for uniqueness
    const key = `${content.trim().toLowerCase()}_${Date.now()}`;
    this.recentlySentMessages.add(key);

    // Also track by message ID if provided
    if (messageId) {
      this.recentlySentMessages.add(messageId);
    }

    // Clean up old entries after 30 seconds to prevent memory leaks
    setTimeout(() => {
      this.recentlySentMessages.delete(key);
      if (messageId) {
        this.recentlySentMessages.delete(messageId);
      }
    }, 30000);

    console.log(`[MessagePolling] Tracking recently sent message: ${content.substring(0, 50)}...`);
  }

  /**
   * Update tracking when a temporary message ID is replaced with a real ID
   */
  updateMessageIdTracking(oldId: string, newId: string, content: string): void {
    // Remove old ID tracking
    this.recentlySentMessages.delete(oldId);

    // Add new ID tracking
    this.recentlySentMessages.add(newId);

    // Also add content-based tracking to be extra safe
    const contentKey = `${content.trim().toLowerCase()}_${Date.now()}`;
    this.recentlySentMessages.add(contentKey);

    console.log(`[MessagePolling] Updated message tracking: ${oldId} -> ${newId}`);

    // Clean up after 30 seconds
    setTimeout(() => {
      this.recentlySentMessages.delete(newId);
      this.recentlySentMessages.delete(contentKey);
    }, 30000);
  }

  /**
   * Initialize sync time from existing messages (for first-time setup)
   */
  initializeSyncTimeFromMessages(messages: MessageType[]): void {
    if (messages.length === 0) {
      return;
    }

    // Find the most recent message date
    const sortedMessages = messages
      .filter(msg => msg.Date && !msg.isTemporary)
      .sort((a, b) => new Date(b.Date!).getTime() - new Date(a.Date!).getTime());

    if (sortedMessages.length > 0) {
      // Apply safety buffer for initialization too
      const newestMessageTime = new Date(sortedMessages[0].Date!);
      const safetyBufferMs = 2000; // 2 seconds buffer
      const safeSyncTime = new Date(newestMessageTime.getTime() - safetyBufferMs);
      const safeSyncTimeISO = safeSyncTime.toISOString();

      SyncTimeManager.setLastSyncTime(this.config.channelId, safeSyncTimeISO);
      console.log(`[MessagePolling] Initialized sync time with safety buffer: ${safeSyncTimeISO} (${safetyBufferMs}ms before newest: ${sortedMessages[0].Date!})`);
    }
  }

  /**
   * Poll for new messages since the last sync time (incremental sync)
   */
  private async pollForNewMessages(): Promise<void> {
    try {
      const lastSyncTime = SyncTimeManager.getLastSyncTime(this.config.channelId);

      console.log(`[MessagePolling] Polling for messages since: ${lastSyncTime}`);

      // Fetch messages since last sync with smaller limit (only new messages expected)
      const messages = await getMessagesSinceTimestamp(
        this.config.channelId,
        lastSyncTime, // Only get messages since this timestamp
        20, // Smaller limit since we're only getting new messages
        this.config.coachName || "all",
        [...UI_DEFAULT_TYPES] // Convert readonly array to mutable array
      );

      if (!messages || messages.length === 0) {
        console.log("[MessagePolling] No messages returned from server");
        return;
      }

      // Filter out recently sent messages from this session (database already filtered by timestamp)
      const newMessages = this.filterRecentlySentMessages(messages);

      if (newMessages.length > 0) {
        console.log(`[MessagePolling] Found ${newMessages.length} new messages since ${lastSyncTime}`);

        // Update sync time to the newest message date
        this.updateSyncTimeFromMessages(newMessages);

        // Notify callback about new messages
        if (this.config.onNewMessages) {
          this.config.onNewMessages(newMessages);
        }
      } else {
        console.log("[MessagePolling] No new messages found since last sync");
      }
    } catch (error) {
      console.error("[MessagePolling] Error polling for messages:", error);
      if (this.config.onError) {
        this.config.onError(error as Error);
      }
    }
  }

  /**
   * Filter out recently sent messages from this session
   * (Database query already filters by timestamp, so we only need to filter session-specific messages)
   */
  private filterRecentlySentMessages(messages: MessageType[]): MessageType[] {
    return messages.filter(message => {
      if (!message.Date || message.isTemporary) {
        return false;
      }

      // Check if this message was recently sent by this session
      const contentKey = `${message.Content.trim().toLowerCase()}_`;
      const isRecentlySent = Array.from(this.recentlySentMessages).some(key =>
        key.startsWith(contentKey) || key === message.Id
      );

      if (isRecentlySent) {
        console.log(`[MessagePolling] Filtering out recently sent message: ${message.Content.substring(0, 50)}...`);
        return false;
      }

      // Additional check: if message was sent very recently (within last 10 seconds) by current user,
      // it's likely from this session
      if (message.Sender === "user") {
        const messageTime = new Date(message.Date).getTime();
        const tenSecondsAgo = Date.now() - 10000;
        if (messageTime > tenSecondsAgo) {
          console.log(`[MessagePolling] Filtering out very recent user message: ${message.Content.substring(0, 50)}...`);
          return false;
        }
      }

      return true;
    });
  }

  /**
   * Update the sync time from a list of new messages with safety buffer
   */
  private updateSyncTimeFromMessages(newMessages: MessageType[]): void {
    if (newMessages.length === 0) {
      return;
    }

    // Find the most recent message date
    const sortedMessages = newMessages
      .filter(msg => msg.Date && !msg.isTemporary)
      .sort((a, b) => new Date(b.Date!).getTime() - new Date(a.Date!).getTime());

    if (sortedMessages.length > 0) {
      const newestMessageTime = new Date(sortedMessages[0].Date!);

      // CRITICAL FIX: Subtract 2 seconds as safety buffer to prevent missing messages
      // This accounts for:
      // 1. Database timestamp precision differences
      // 2. Network latency between message save and poll
      // 3. Race conditions between concurrent operations
      const safetyBufferMs = 2000; // 2 seconds
      const safeSyncTime = new Date(newestMessageTime.getTime() - safetyBufferMs);
      const safeSyncTimeISO = safeSyncTime.toISOString();

      SyncTimeManager.setLastSyncTime(this.config.channelId, safeSyncTimeISO);
      console.log(`[MessagePolling] Updated sync time to: ${safeSyncTimeISO} (${safetyBufferMs}ms before newest message: ${sortedMessages[0].Date!})`);
    }
  }

  /**
   * Update polling configuration
   */
  updateConfig(newConfig: Partial<PollingConfig>): void {
    const wasPolling = this.isPolling;
    
    if (wasPolling) {
      this.stop();
    }
    
    this.config = { ...this.config, ...newConfig };
    
    if (wasPolling) {
      this.start();
    }
  }

  /**
   * Check if currently polling
   */
  get polling(): boolean {
    return this.isPolling;
  }

  /**
   * Get current configuration for debugging
   */
  get currentConfig(): PollingConfig {
    return { ...this.config };
  }

  /**
   * Get last sync time for debugging
   */
  get lastKnownMessageDate(): string | null {
    return SyncTimeManager.getLastSyncTime(this.config.channelId);
  }

  /**
   * Force a poll immediately (for testing)
   */
  async forcePoll(): Promise<void> {
    if (!this.isPolling) {
      console.warn("[MessagePolling] Cannot force poll - service is not running");
      return;
    }
    await this.pollForNewMessages();
  }

  /**
   * Clear sync time for this channel (for testing/reset)
   */
  clearSyncTime(): void {
    SyncTimeManager.clearSyncTime(this.config.channelId);
  }
}

/**
 * Utility function to deduplicate messages based on their ID and content
 */
export function deduplicateMessages(existingMessages: MessageType[], newMessages: MessageType[]): MessageType[] {
  const existingIds = new Set(existingMessages.map(msg => msg.Id));

  // Also create a set of content+sender+date combinations to catch duplicates with different IDs
  const existingContentKeys = new Set(
    existingMessages
      .filter(msg => !msg.isTemporary) // Only consider non-temporary messages for content matching
      .map(msg => `${msg.Content.trim().toLowerCase()}_${msg.Sender}_${msg.Date}`)
  );

  return newMessages.filter(msg => {
    // Skip if ID already exists
    if (existingIds.has(msg.Id)) {
      return false;
    }

    // Skip if content+sender+date combination already exists (handles temp->real ID changes)
    const contentKey = `${msg.Content.trim().toLowerCase()}_${msg.Sender}_${msg.Date}`;
    if (existingContentKeys.has(contentKey)) {
      console.log(`[Deduplication] Filtering duplicate content: ${msg.Content.substring(0, 50)}...`);
      return false;
    }

    return true;
  });
}

/**
 * Utility function to merge new messages into existing messages while maintaining chronological order
 */
export function mergeMessages(existingMessages: MessageType[], newMessages: MessageType[]): MessageType[] {
  // Deduplicate first
  const uniqueNewMessages = deduplicateMessages(existingMessages, newMessages);
  
  if (uniqueNewMessages.length === 0) {
    return existingMessages;
  }

  // Combine and sort by date
  const allMessages = [...existingMessages, ...uniqueNewMessages];
  
  return allMessages.sort((a, b) => {
    const dateA = a.Date ? new Date(a.Date).getTime() : 0;
    const dateB = b.Date ? new Date(b.Date).getTime() : 0;
    return dateA - dateB; // Oldest first
  });
}
