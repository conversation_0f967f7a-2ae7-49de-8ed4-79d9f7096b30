"use client";

import { getMessagesSinceTimestamp } from "../action.ts";
import { UI_DEFAULT_TYPES } from "./message-types.ts";

export interface MessageType {
  Id: string;
  Date?: string;
  Sender: string;
  Type?: string;
  Content: string;
  CoachName?: string;
  Audio?: string | Uint8Array;
  Status?: string;
  isTemporary?: boolean;
  New?: boolean;
}

export interface PollingConfig {
  channelId: string | number;
  coachName?: string;
  intervalMs?: number;
  onNewMessages?: (messages: MessageType[]) => void;
  onError?: (error: Error) => void;
}

/**
 * Client-side sync time manager for incremental message polling
 */
export class SyncTimeManager {
  private static readonly STORAGE_KEY = 'awaken_last_sync_time';

  /**
   * Get the last sync time for a channel, or current time for first load
   */
  static getLastSyncTime(channelId: string | number): string {
    const key = `${this.STORAGE_KEY}_${channelId}`;
    const stored = localStorage.getItem(key);

    if (stored) {
      console.log(`[SyncTime] Retrieved last sync time for channel ${channelId}: ${stored}`);
      return stored;
    }

    // First time - use current time to avoid fetching all historical messages
    const currentTime = new Date().toISOString();
    console.log(`[SyncTime] First time sync for channel ${channelId}, using current time: ${currentTime}`);
    this.setLastSyncTime(channelId, currentTime);
    return currentTime;
  }

  /**
   * Update the last sync time for a channel
   */
  static setLastSyncTime(channelId: string | number, timestamp: string): void {
    const key = `${this.STORAGE_KEY}_${channelId}`;
    localStorage.setItem(key, timestamp);
    console.log(`[SyncTime] Updated last sync time for channel ${channelId}: ${timestamp}`);
  }

  /**
   * Clear sync time (useful for testing or reset)
   */
  static clearSyncTime(channelId: string | number): void {
    const key = `${this.STORAGE_KEY}_${channelId}`;
    localStorage.removeItem(key);
    console.log(`[SyncTime] Cleared sync time for channel ${channelId}`);
  }
}

export class MessagePollingService {
  private intervalId: number | null = null;
  private isPolling = false;
  private config: PollingConfig;
  private readonly DEFAULT_INTERVAL = 5000; // 5 seconds
  private recentlySentMessageIds: Set<string> = new Set(); // Track recently sent message IDs
  private recentlySentMessageContent: Map<string, string> = new Map(); // Map ID to content for fallback

  constructor(config: PollingConfig) {
    this.config = {
      intervalMs: this.DEFAULT_INTERVAL,
      ...config,
    };
  }

  /**
   * Start polling for new messages using incremental sync
   */
  start(): void {
    if (this.isPolling) {
      console.log("[MessagePolling] Already polling, ignoring start request");
      return;
    }

    console.log(`[MessagePolling] Starting incremental polling for channel ${this.config.channelId} every ${this.config.intervalMs}ms`);
    this.isPolling = true;

    // Poll immediately on start
    this.pollForNewMessages();

    // Set up interval for subsequent polls
    this.intervalId = globalThis.setInterval(() => {
      this.pollForNewMessages();
    }, this.config.intervalMs);
  }

  /**
   * Stop polling for new messages
   */
  stop(): void {
    if (!this.isPolling) {
      return;
    }

    console.log(`[MessagePolling] Stopping polling for channel ${this.config.channelId}`);
    this.isPolling = false;
    
    if (this.intervalId !== null) {
      globalThis.clearInterval(this.intervalId);
      this.intervalId = null;
    }
  }

  /**
   * Update the last sync time for this channel
   */
  updateLastSyncTime(timestamp: string): void {
    SyncTimeManager.setLastSyncTime(this.config.channelId, timestamp);
  }

  /**
   * Track a recently sent message to avoid duplicates from polling
   */
  trackRecentlySentMessage(content: string, messageId?: string): void {
    if (messageId) {
      // Primary tracking by message ID
      this.recentlySentMessageIds.add(messageId);
      this.recentlySentMessageContent.set(messageId, content.trim().toLowerCase());

      // Clean up after 60 seconds to prevent memory leaks
      setTimeout(() => {
        this.recentlySentMessageIds.delete(messageId);
        this.recentlySentMessageContent.delete(messageId);
      }, 60000);

      console.log(`[MessagePolling] Tracking recently sent message by ID: ${messageId} - ${content.substring(0, 50)}...`);
    } else {
      // Fallback: track by content with timestamp (for temporary messages)
      const tempKey = `temp_${content.trim().toLowerCase()}_${Date.now()}`;
      this.recentlySentMessageIds.add(tempKey);
      this.recentlySentMessageContent.set(tempKey, content.trim().toLowerCase());

      // Clean up after 30 seconds for temporary tracking
      setTimeout(() => {
        this.recentlySentMessageIds.delete(tempKey);
        this.recentlySentMessageContent.delete(tempKey);
      }, 30000);

      console.log(`[MessagePolling] Tracking recently sent message by content (temp): ${content.substring(0, 50)}...`);
    }
  }

  /**
   * Update tracking when a temporary message ID is replaced with a real ID
   */
  updateMessageIdTracking(oldId: string, newId: string, content: string): void {
    // Remove old ID tracking
    this.recentlySentMessageIds.delete(oldId);
    this.recentlySentMessageContent.delete(oldId);

    // Add new ID tracking
    this.recentlySentMessageIds.add(newId);
    this.recentlySentMessageContent.set(newId, content.trim().toLowerCase());

    console.log(`[MessagePolling] Updated message tracking: ${oldId} -> ${newId}`);

    // Clean up after 60 seconds
    setTimeout(() => {
      this.recentlySentMessageIds.delete(newId);
      this.recentlySentMessageContent.delete(newId);
    }, 60000);
  }

  /**
   * Initialize sync time from existing messages (for first-time setup)
   */
  initializeSyncTimeFromMessages(messages: MessageType[]): void {
    if (messages.length === 0) {
      return;
    }

    // Find the most recent message date
    const sortedMessages = messages
      .filter(msg => msg.Date && !msg.isTemporary)
      .sort((a, b) => new Date(b.Date!).getTime() - new Date(a.Date!).getTime());

    if (sortedMessages.length > 0) {
      // Apply safety buffer for initialization too
      const newestMessageTime = new Date(sortedMessages[0].Date!);
      const safetyBufferMs = 2000; // 2 seconds buffer
      const safeSyncTime = new Date(newestMessageTime.getTime() - safetyBufferMs);
      const safeSyncTimeISO = safeSyncTime.toISOString();

      SyncTimeManager.setLastSyncTime(this.config.channelId, safeSyncTimeISO);
      console.log(`[MessagePolling] Initialized sync time with safety buffer: ${safeSyncTimeISO} (${safetyBufferMs}ms before newest: ${sortedMessages[0].Date!})`);
    }
  }

  /**
   * Poll for new messages since the last sync time (incremental sync)
   */
  private async pollForNewMessages(): Promise<void> {
    try {
      const lastSyncTime = SyncTimeManager.getLastSyncTime(this.config.channelId);

      console.log(`[MessagePolling] Polling for messages since: ${lastSyncTime}`);

      // Fetch messages since last sync with smaller limit (only new messages expected)
      const messages = await getMessagesSinceTimestamp(
        this.config.channelId,
        lastSyncTime, // Only get messages since this timestamp
        20, // Smaller limit since we're only getting new messages
        this.config.coachName || "all",
        [...UI_DEFAULT_TYPES] // Convert readonly array to mutable array
      );

      if (!messages || messages.length === 0) {
        console.log("[MessagePolling] No messages returned from server");
        return;
      }

      // Filter out recently sent messages from this session (database already filtered by timestamp)
      const newMessages = this.filterRecentlySentMessages(messages);

      if (newMessages.length > 0) {
        console.log(`[MessagePolling] Found ${newMessages.length} new messages since ${lastSyncTime}`);

        // Update sync time to the newest message date
        this.updateSyncTimeFromMessages(newMessages);

        // Notify callback about new messages
        if (this.config.onNewMessages) {
          this.config.onNewMessages(newMessages);
        }
      } else {
        console.log("[MessagePolling] No new messages found since last sync");
      }
    } catch (error) {
      console.error("[MessagePolling] Error polling for messages:", error);
      if (this.config.onError) {
        this.config.onError(error as Error);
      }
    }
  }

  /**
   * Filter out recently sent messages from this session
   * (Database query already filters by timestamp, so we only need to filter session-specific messages)
   */
  private filterRecentlySentMessages(messages: MessageType[]): MessageType[] {
    return messages.filter(message => {
      if (!message.Date || message.isTemporary) {
        return false;
      }

      // Primary check: filter by message ID
      if (this.recentlySentMessageIds.has(message.Id)) {
        console.log(`[MessagePolling] Filtering out recently sent message by ID: ${message.Id} - ${message.Content.substring(0, 50)}...`);
        return false;
      }

      // Secondary check: filter by content for fallback cases
      const messageContent = message.Content.trim().toLowerCase();
      const isRecentlySent = Array.from(this.recentlySentMessageContent.values()).includes(messageContent);

      if (isRecentlySent) {
        console.log(`[MessagePolling] Filtering out recently sent message by content: ${message.Content.substring(0, 50)}...`);
        return false;
      }

      // Additional check: if message was sent very recently (within last 10 seconds) by current user,
      // it's likely from this session
      if (message.Sender === "user") {
        const messageTime = new Date(message.Date).getTime();
        const tenSecondsAgo = Date.now() - 10000;
        if (messageTime > tenSecondsAgo) {
          console.log(`[MessagePolling] Filtering out very recent user message: ${message.Content.substring(0, 50)}...`);
          return false;
        }
      }

      return true;
    });
  }

  /**
   * Update the sync time from a list of new messages with safety buffer
   */
  private updateSyncTimeFromMessages(newMessages: MessageType[]): void {
    if (newMessages.length === 0) {
      return;
    }

    // Find the most recent message date
    const sortedMessages = newMessages
      .filter(msg => msg.Date && !msg.isTemporary)
      .sort((a, b) => new Date(b.Date!).getTime() - new Date(a.Date!).getTime());

    if (sortedMessages.length > 0) {
      const newestMessageTime = new Date(sortedMessages[0].Date!);

      // CRITICAL FIX: Subtract 2 seconds as safety buffer to prevent missing messages
      // This accounts for:
      // 1. Database timestamp precision differences
      // 2. Network latency between message save and poll
      // 3. Race conditions between concurrent operations
      const safetyBufferMs = 2000; // 2 seconds
      const safeSyncTime = new Date(newestMessageTime.getTime() - safetyBufferMs);
      const safeSyncTimeISO = safeSyncTime.toISOString();

      SyncTimeManager.setLastSyncTime(this.config.channelId, safeSyncTimeISO);
      console.log(`[MessagePolling] Updated sync time to: ${safeSyncTimeISO} (${safetyBufferMs}ms before newest message: ${sortedMessages[0].Date!})`);
    }
  }

  /**
   * Update polling configuration
   */
  updateConfig(newConfig: Partial<PollingConfig>): void {
    const wasPolling = this.isPolling;
    
    if (wasPolling) {
      this.stop();
    }
    
    this.config = { ...this.config, ...newConfig };
    
    if (wasPolling) {
      this.start();
    }
  }

  /**
   * Check if currently polling
   */
  get polling(): boolean {
    return this.isPolling;
  }

  /**
   * Get current configuration for debugging
   */
  get currentConfig(): PollingConfig {
    return { ...this.config };
  }

  /**
   * Get last sync time for debugging
   */
  get lastKnownMessageDate(): string | null {
    return SyncTimeManager.getLastSyncTime(this.config.channelId);
  }

  /**
   * Force a poll immediately (for testing)
   */
  async forcePoll(): Promise<void> {
    if (!this.isPolling) {
      console.warn("[MessagePolling] Cannot force poll - service is not running");
      return;
    }
    await this.pollForNewMessages();
  }

  /**
   * Clear sync time for this channel (for testing/reset)
   */
  clearSyncTime(): void {
    SyncTimeManager.clearSyncTime(this.config.channelId);
  }
}

/**
 * Utility function to deduplicate messages based on their ID with strict ID-based deduplication
 * This ensures message state works like a Set with unique IDs
 */
export function deduplicateMessages(existingMessages: MessageType[], newMessages: MessageType[]): MessageType[] {
  // Create a Set of existing message IDs for O(1) lookup
  const existingIds = new Set(existingMessages.map(msg => msg.Id));

  // Filter out any new messages that have IDs already present in existing messages
  const uniqueNewMessages = newMessages.filter(msg => {
    if (existingIds.has(msg.Id)) {
      console.log(`[Deduplication] Filtering duplicate ID: ${msg.Id} - ${msg.Content.substring(0, 50)}...`);
      return false;
    }
    return true;
  });

  // Additional safety check: ensure no duplicates within the new messages themselves
  const seenNewIds = new Set<string>();
  const finalUniqueMessages = uniqueNewMessages.filter(msg => {
    if (seenNewIds.has(msg.Id)) {
      console.log(`[Deduplication] Filtering duplicate within new messages: ${msg.Id} - ${msg.Content.substring(0, 50)}...`);
      return false;
    }
    seenNewIds.add(msg.Id);
    return true;
  });

  console.log(`[Deduplication] Filtered ${newMessages.length} new messages to ${finalUniqueMessages.length} unique messages`);
  return finalUniqueMessages;
}

/**
 * Utility function to merge new messages into existing messages while maintaining chronological order
 * This function ensures message state works like a Set with unique IDs
 */
export function mergeMessages(existingMessages: MessageType[], newMessages: MessageType[]): MessageType[] {
  if (!newMessages || newMessages.length === 0) {
    return existingMessages;
  }

  console.log(`[MergeMessages] Merging ${existingMessages.length} existing + ${newMessages.length} new messages`);

  // Deduplicate first using strict ID-based deduplication
  const uniqueNewMessages = deduplicateMessages(existingMessages, newMessages);

  if (uniqueNewMessages.length === 0) {
    console.log(`[MergeMessages] No unique new messages to merge`);
    return existingMessages;
  }

  // Combine and sort by date
  const allMessages = [...existingMessages, ...uniqueNewMessages];

  const sortedMessages = allMessages.sort((a, b) => {
    const dateA = a.Date ? new Date(a.Date).getTime() : 0;
    const dateB = b.Date ? new Date(b.Date).getTime() : 0;
    return dateA - dateB; // Oldest first
  });

  // Final safety check: ensure no duplicate IDs in the final result
  const finalIds = new Set<string>();
  const finalMessages = sortedMessages.filter(msg => {
    if (finalIds.has(msg.Id)) {
      console.warn(`[MergeMessages] Found duplicate ID in final result: ${msg.Id}, removing duplicate`);
      return false;
    }
    finalIds.add(msg.Id);
    return true;
  });

  console.log(`[MergeMessages] Final result: ${finalMessages.length} messages`);
  return finalMessages;
}
