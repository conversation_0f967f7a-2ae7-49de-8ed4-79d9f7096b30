/**
 * Heat-up module for comprehensive health checks
 * This module imports and invokes all server actions with invalid values
 * to ensure they are compiled and ready when Reframe compiles files on the run
 */

// Core action imports from action.ts
import {
  decryptProfile,
  getProfileText,
  getAllMessages,
  processTranscriptAction,
  getCoachPrompt,
  encryptProfile,
  saveMetafeedback,
  getFirstMessage,
  decryptMessages,
  getAppVersion,
  getLatestNotice,
  updateLastSeenNotice,
  dummy,
  getMetaFeedback,
  updateProfileIfNeeded,
  updateProfileText,
  initiateProfile,
  getCoachPromptAndTools,
  updateTimeZone,
  generateEnrolmentMessage,
  getStripePrices,
  checkCallLimit,
  generateDailyAwakeningForUser,
  setFirstMessageFalse,
  getSummaryInfo,
  completeOnboarding,
  getUsage,
  updateMetaFeedback,
  setProfileText,
  updateSubscriptionUsage
} from "../action.ts";

// Database conversation actions
import {
  saveMessage,
  getMessages,
  getStarredMessages,
  updateMessageStatus,
  prepMessageHistoryAction,
  getAllMessagesRaw,
  uploadAudio,
} from "../actions/db/conversation-actions.ts";

// AI call actions
import {
  buildGoalCallAssistantConfig,
  handleCallEnd,
  buildCallAssistantConfig,
  trackCall,
  buildGuidedCallAssistantConfig,
  getVapiConfig,
  storeCallControlUrl,
  getControlUrlForCall
} from "../actions/ai/call-actions.ts";

// AI tool handler
import { handleVapiToolCall } from "../actions/ai/tool-handler.ts";

// Coach actions
import {
  getUserCoachesAction,
  getCoachNameFromSlugAction,
  getAllCoachesAction,
  getCoachAction,
  getUserCardsForCoachAction,
  initiateUserCardCreationAction,
  completeUserCardCreationAction
} from "../actions/db/coach-actions.ts";

// Stripe actions
import {
  handleWebhookEvent,
  createGiftSession,
  createCheckoutSession,
  upgradeSubscription
} from "../actions/stripe-actions.ts";

// AI prompt actions
import {
  prepareUserPromptClaudeAction,
  prepareGeminiPayloadAction
} from "../actions/ai/prompt-actions.ts";

// AI session plan actions
import {
  generateGuidedSessionPlanAction
} from "../actions/ai/session-plan-actions.ts";


// Helper functions
import { getAuthenticatedUser } from "../lib/auth-helper.ts";
import { getUserInfo } from "../lib/get-user-info.ts";
import { getActionCost } from "../lib/user-actions.ts";

/**
 * Safe wrapper function that executes a function with invalid parameters
 * and catches any errors (which is expected behavior for health checks)
 */
const safeInvoke = async (name: string, fn: () => Promise<any>): Promise<void> => {
  try {
    await fn();
    console.log(`[HEAT-UP] ${name}: invoked successfully (unexpected)`);
  } catch (error) {
    console.log(`[HEAT-UP] ${name}: failed as expected`);
  }
};

/**
 * Comprehensive health check function that invokes all server actions
 * with invalid values to ensure they are compiled and ready
 */
export const heatUpServerActions = async (): Promise<void> => {
  console.log("[HEAT-UP] Starting comprehensive server action heat-up...");

  const startTime = Date.now();

  // Core action.ts functions
  await safeInvoke("decryptProfile", () => decryptProfile("invalid-channel"));
  await safeInvoke("getProfileText", () => getProfileText("invalid-channel"));
  await safeInvoke("getAllMessages", () => getAllMessages(-1, 1));
  await safeInvoke("processTranscriptAction", () => processTranscriptAction("invalid", "invalid"));
  await safeInvoke("getCoachPrompt", () => getCoachPrompt("invalid", "invalid"));
  await safeInvoke("encryptProfile", () => encryptProfile("invalid"));
  await safeInvoke("saveMetafeedback", () => saveMetafeedback("invalid", "invalid", "invalid"));
  await safeInvoke("getFirstMessage", () => getFirstMessage("invalid"));
  await safeInvoke("decryptMessages", () => decryptMessages("invalid"));
  await safeInvoke("getAppVersion", () => getAppVersion());
  await safeInvoke("getLatestNotice", () => getLatestNotice("invalid"));
  await safeInvoke("updateLastSeenNotice", () => updateLastSeenNotice("invalid"));
  await safeInvoke("dummy", () => dummy());
  await safeInvoke("getMetaFeedback", () => getMetaFeedback("invalid", "invalid"));
  await safeInvoke("updateProfileIfNeeded", () => updateProfileIfNeeded("invalid", "invalid"));
  await safeInvoke("updateProfileText", () => updateProfileText("invalid", "invalid"));
  await safeInvoke("initiateProfile", () => initiateProfile("invalid"));
  await safeInvoke("getCoachPromptAndTools", () => getCoachPromptAndTools("invalid", "invalid"));
  await safeInvoke("updateTimeZone", () => updateTimeZone("invalid", "invalid"));
  await safeInvoke("generateEnrolmentMessage", () => generateEnrolmentMessage("invalid"));
  await safeInvoke("getStripePrices", () => getStripePrices());
  await safeInvoke("checkCallLimit", () => checkCallLimit("invalid"));
  await safeInvoke("generateDailyAwakeningForUser", () => generateDailyAwakeningForUser("invalid"));
  await safeInvoke("setFirstMessageFalse", () => setFirstMessageFalse("invalid"));
  await safeInvoke("getSummaryInfo", () => getSummaryInfo("invalid"));
  await safeInvoke("completeOnboarding", () => completeOnboarding("invalid", {}));
  await safeInvoke("getUsage", () => getUsage("invalid"));
  await safeInvoke("updateMetaFeedback", () => updateMetaFeedback("invalid", "invalid"));
  await safeInvoke("setProfileText", () => setProfileText("invalid", "invalid"));
  await safeInvoke("updateSubscriptionUsage", () => updateSubscriptionUsage("invalid", 0, 0));

  // Database conversation actions
  await safeInvoke("saveMessage", () => saveMessage("invalid", "invalid", "invalid", "invalid"));
  await safeInvoke("getMessages", () => getMessages("invalid"));
  await safeInvoke("getStarredMessages", () => getStarredMessages(-1));
  await safeInvoke("updateMessageStatus", () => updateMessageStatus("invalid", "invalid"));
  await safeInvoke("prepMessageHistoryAction", () => Promise.resolve(prepMessageHistoryAction([])));
  await safeInvoke("getAllMessagesRaw", () => getAllMessagesRaw("invalid"));
  await safeInvoke("uploadAudio", () => uploadAudio(new Blob(), "invalid"));

  // AI call actions
  await safeInvoke("buildGoalCallAssistantConfig", () => buildGoalCallAssistantConfig("invalid"));
  await safeInvoke("handleCallEnd", () => handleCallEnd({}));
  await safeInvoke("buildCallAssistantConfig", () => buildCallAssistantConfig("invalid", "normal"));
  await safeInvoke("trackCall", () => trackCall("invalid", "invalid", "invalid", "invalid"));
  await safeInvoke("buildGuidedCallAssistantConfig", () => buildGuidedCallAssistantConfig("invalid", "invalid", "invalid"));
  await safeInvoke("getVapiConfig", () => getVapiConfig("invalid", false, null));
  await safeInvoke("storeCallControlUrl", () => storeCallControlUrl("invalid", "invalid", "invalid"));
  await safeInvoke("getControlUrlForCall", () => getControlUrlForCall("invalid"));

  // AI tool handler
  await safeInvoke("handleVapiToolCall", () => handleVapiToolCall(new Request("http://invalid")));

  // Coach actions
  await safeInvoke("getUserCoachesAction", () => getUserCoachesAction("invalid"));
  await safeInvoke("getCoachNameFromSlugAction", () => getCoachNameFromSlugAction("invalid"));
  await safeInvoke("getAllCoachesAction", () => getAllCoachesAction());
  await safeInvoke("getCoachAction", () => getCoachAction("invalid"));
  await safeInvoke("getUserCardsForCoachAction", () => getUserCardsForCoachAction(-1, "invalid"));
  await safeInvoke("initiateUserCardCreationAction", () => initiateUserCardCreationAction(-1, "invalid", "invalid"));
  await safeInvoke("completeUserCardCreationAction", () => completeUserCardCreationAction("invalid", {}));

  // Stripe actions
  await safeInvoke("handleWebhookEvent", () => handleWebhookEvent({}));
  await safeInvoke("createGiftSession", () => createGiftSession("invalid", "invalid", 0, 0, "invalid", "invalid"));
  await safeInvoke("createCheckoutSession", () => createCheckoutSession("invalid", "invalid", "invalid"));
  await safeInvoke("upgradeSubscription", () => upgradeSubscription("invalid", "invalid"));

  // AI prompt actions
  await safeInvoke("prepareUserPromptClaudeAction", () => prepareUserPromptClaudeAction("invalid", "invalid", "invalid"));
  await safeInvoke("prepareGeminiPayloadAction", () => prepareGeminiPayloadAction("invalid", "invalid", "invalid"));

  // AI session plan actions
  await safeInvoke("generateGuidedSessionPlanAction", () => generateGuidedSessionPlanAction("invalid", "invalid", "invalid"));

  // Helper functions
  await safeInvoke("getAuthenticatedUser", () => getAuthenticatedUser());
  await safeInvoke("getUserInfo", () => getUserInfo({ email: "invalid" }));
  await safeInvoke("getActionCost", () => Promise.resolve(getActionCost("message", 1)));

  const endTime = Date.now();
  const duration = endTime - startTime;

  console.log(`[HEAT-UP] Completed comprehensive server action heat-up in ${duration}ms`);
  console.log(`[HEAT-UP] All server actions have been invoked and are ready for compilation`);
};