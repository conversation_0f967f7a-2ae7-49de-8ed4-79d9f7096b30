"use client";

import { getCoachMessagesSinceTimestamp } from "../routes/admin/actions/admin-actions.ts";
import { UI_DEFAULT_TYPES } from "./message-types.ts";

export interface CoachMessageType {
  Id: string;
  Date?: string;
  Sender: string;
  Type?: string;
  Content: string;
  CoachName?: string;
  Audio?: string | Uint8Array;
  Status?: string;
  ChannelId?: number;
  SeenByCoach?: number;
  SeenByUser?: number;
  isTemporary?: boolean;
  New?: boolean;
}

export interface CoachPollingConfig {
  coachEmail: string;
  intervalMs?: number;
  onNewMessages?: (messages: CoachMessageType[]) => void;
  onError?: (error: Error) => void;
}

/**
 * Client-side sync time manager for coach message polling
 */
export class CoachSyncTimeManager {
  private static readonly STORAGE_KEY = 'awaken_coach_last_sync_time';

  /**
   * Get the last sync time for a coach
   */
  static getLastSyncTime(coachEmail: string): string {
    if (typeof localStorage === 'undefined') {
      // Default to 24 hours ago if localStorage is not available
      const defaultTime = new Date(Date.now() - 24 * 60 * 60 * 1000);
      return defaultTime.toISOString();
    }

    try {
      const stored = localStorage.getItem(`${this.STORAGE_KEY}_${coachEmail}`);
      if (stored) {
        return stored;
      }
    } catch (error) {
      console.warn("[CoachSyncTimeManager] Error reading from localStorage:", error);
    }

    // Default to 24 hours ago for first-time polling
    const defaultTime = new Date(Date.now() - 24 * 60 * 60 * 1000);
    return defaultTime.toISOString();
  }

  /**
   * Update the last sync time for a coach
   */
  static setLastSyncTime(coachEmail: string, timestamp: string): void {
    if (typeof localStorage === 'undefined') {
      return;
    }

    try {
      localStorage.setItem(`${this.STORAGE_KEY}_${coachEmail}`, timestamp);
    } catch (error) {
      console.warn("[CoachSyncTimeManager] Error writing to localStorage:", error);
    }
  }
}

export class CoachMessagePollingService {
  private intervalId: number | null = null;
  private isPolling = false;
  private config: CoachPollingConfig;
  private readonly DEFAULT_INTERVAL = 5000; // 5 seconds
  private recentlySentMessages: Set<string> = new Set(); // Track recently sent message content

  constructor(config: CoachPollingConfig) {
    this.config = {
      intervalMs: this.DEFAULT_INTERVAL,
      ...config,
    };
  }

  /**
   * Start polling for new messages using incremental sync
   */
  start(): void {
    if (this.isPolling) {
      return;
    }

    this.isPolling = true;

    // Poll immediately on start
    this.pollForNewMessages();

    // Set up interval for subsequent polls
    this.intervalId = globalThis.setInterval(() => {
      this.pollForNewMessages();
    }, this.config.intervalMs);
  }

  /**
   * Stop polling for messages
   */
  stop(): void {
    if (!this.isPolling) {
      return;
    }

    this.isPolling = false;

    if (this.intervalId !== null) {
      globalThis.clearInterval(this.intervalId);
      this.intervalId = null;
    }
  }

  /**
   * Poll for new messages since the last sync time (incremental sync)
   */
  private async pollForNewMessages(): Promise<void> {
    try {
      const lastSyncTime = CoachSyncTimeManager.getLastSyncTime(this.config.coachEmail);

      // Fetch messages since last sync with smaller limit (only new messages expected)
      const messages = await getCoachMessagesSinceTimestamp(
        this.config.coachEmail,
        lastSyncTime, // Only get messages since this timestamp
        20, // Smaller limit since we're only getting new messages
        [...UI_DEFAULT_TYPES] // Convert readonly array to mutable array
      );

      if (!messages || messages.length === 0) {
        return;
      }

      // Filter out recently sent messages from this session (database already filtered by timestamp)
      const newMessages = this.filterRecentlySentMessages(messages);

      if (newMessages.length > 0) {
        // Update sync time to the newest message date
        this.updateSyncTimeFromMessages(newMessages);

        // Notify callback about new messages
        if (this.config.onNewMessages) {
          this.config.onNewMessages(newMessages);
        }
      }

    } catch (error) {
      console.error("[CoachMessagePolling] Error during polling:", error);
      if (this.config.onError) {
        this.config.onError(error as Error);
      }
    }
  }

  /**
   * Filter out messages that were recently sent by this client session
   */
  private filterRecentlySentMessages(messages: CoachMessageType[]): CoachMessageType[] {
    return messages.filter(message => {
      // Only filter out messages sent by the coach (not user messages)
      if (message.Sender === "coach" && this.recentlySentMessages.has(message.Content)) {
        return false;
      }
      return true;
    });
  }

  /**
   * Update sync time based on the newest message in the array
   */
  private updateSyncTimeFromMessages(messages: CoachMessageType[]): void {
    if (messages.length === 0) return;

    // Find the newest message by date
    const newestMessage = messages.reduce((newest, current) => {
      if (!current.Date) return newest;
      if (!newest.Date) return current;
      return new Date(current.Date) > new Date(newest.Date) ? current : newest;
    });

    if (newestMessage.Date) {
      // Apply safety buffer when updating sync time
      const messageTime = new Date(newestMessage.Date);
      const safetyBufferMs = 1000; // 1 second buffer
      const safeSyncTime = new Date(messageTime.getTime() + safetyBufferMs);

      CoachSyncTimeManager.setLastSyncTime(this.config.coachEmail, safeSyncTime.toISOString());
    }
  }

  /**
   * Track a recently sent message to prevent duplicates
   */
  trackRecentlySentMessage(content: string): void {
    this.recentlySentMessages.add(content);

    // Clean up after 30 seconds to prevent memory leaks
    setTimeout(() => {
      this.recentlySentMessages.delete(content);
    }, 30000);
  }

  /**
   * Initialize sync time from existing messages if this is the first time
   */
  initializeSyncTimeFromMessages(existingMessages: CoachMessageType[]): void {
    const currentSyncTime = CoachSyncTimeManager.getLastSyncTime(this.config.coachEmail);
    const defaultTime = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();

    // Only initialize if we're using the default time (first time)
    if (currentSyncTime === defaultTime && existingMessages.length > 0) {
      this.updateSyncTimeFromMessages(existingMessages);
    }
  }

  /**
   * Update last sync time manually (useful after sending messages)
   */
  updateLastSyncTime(timestamp: string): void {
    CoachSyncTimeManager.setLastSyncTime(this.config.coachEmail, timestamp);
  }

  /**
   * Update polling configuration
   */
  updateConfig(newConfig: Partial<CoachPollingConfig>): void {
    const wasPolling = this.isPolling;
    
    if (wasPolling) {
      this.stop();
    }
    
    this.config = { ...this.config, ...newConfig };
    
    if (wasPolling) {
      this.start();
    }
  }

  /**
   * Check if currently polling
   */
  get polling(): boolean {
    return this.isPolling;
  }
}

/**
 * Merge new messages with existing messages, avoiding duplicates
 */
export const mergeCoachMessages = (
  existingMessages: CoachMessageType[],
  newMessages: CoachMessageType[]
): CoachMessageType[] => {
  if (newMessages.length === 0) return existingMessages;

  const existingIds = new Set(existingMessages.map(msg => msg.Id));
  const uniqueNewMessages = newMessages.filter(msg => !existingIds.has(msg.Id));

  if (uniqueNewMessages.length === 0) {
    return existingMessages;
  }

  // Combine and sort by date
  const allMessages = [...existingMessages, ...uniqueNewMessages];
  return allMessages.sort((a, b) => {
    if (!a.Date || !b.Date) return 0;
    return new Date(a.Date).getTime() - new Date(b.Date).getTime();
  });
};
