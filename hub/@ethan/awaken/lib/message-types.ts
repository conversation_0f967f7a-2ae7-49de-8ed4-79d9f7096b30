/**
 * Centralized message type constants for the Awaken application.
 * This ensures consistency across all message handling code.
 */

export const MESSAGE_TYPES = {
  MESSAGE: 'message',
  CALL: 'call',
  SUMMARY: 'summary',
  COACH_MESSAGE: 'coach_message',
  DAILY_MESSAGE: 'daily_message',
  PROACTIVE_MESSAGE: 'proactive_message'
} as const;

export type MessageType = typeof MESSAGE_TYPES[keyof typeof MESSAGE_TYPES];

/**
 * Message types that should be visible in the user interface.
 * Used for fetching messages in chat views.
 */
export const USER_VISIBLE_MESSAGE_TYPES = [
  MESSAGE_TYPES.MESSAGE,
  MESSAGE_TYPES.CALL,
  MESSAGE_TYPES.COACH_MESSAGE,
  MESSAGE_TYPES.PROACTIVE_MESSAGE
] as const;

/**
 * Default message types for standard chat queries.
 * Excludes specialized types like daily_message.
 */
export const UI_DEFAULT_TYPES = [
  MESSAGE_TYPES.MESSAGE,
  MESSAGE_TYPES.CALL,
  MESSAGE_TYPES.COACH_MESSAGE,
  MESSAGE_TYPES.PROACTIVE_MESSAGE
] as const;

/**
 * Message types visible to coaches/admins in the admin interface.
 * Includes summary messages for coach visibility.
 */
export const ADMIN_VISIBLE_TYPES = [
  MESSAGE_TYPES.MESSAGE,
  MESSAGE_TYPES.CALL,
  MESSAGE_TYPES.COACH_MESSAGE,
  MESSAGE_TYPES.PROACTIVE_MESSAGE,
  MESSAGE_TYPES.SUMMARY
] as const;

/**
 * Message types that represent textual content (not system messages).
 * Used for analytics, digests, and filtering.
 */
export const TEXTUAL_MSG_TYPES = [
  MESSAGE_TYPES.MESSAGE,
  MESSAGE_TYPES.CALL,
  MESSAGE_TYPES.PROACTIVE_MESSAGE
] as const;

/**
 * Helper function to check if a message type is a call message.
 */
export function isCallMessage(messageType: string): boolean {
  return messageType === MESSAGE_TYPES.CALL;
}

/**
 * Helper function for backward compatibility.
 * Treats 'call' messages as regular messages for legacy code paths.
 */
export function normalizeMessageTypeForDisplay(messageType: string): string {
  return messageType === MESSAGE_TYPES.CALL ? MESSAGE_TYPES.MESSAGE : messageType;
}
