"use client";

import React, { useEffect, useRef } from "npm:react@canary";
import { Button, Text } from "@reframe/ui/main.tsx";
import { motion } from "npm:framer-motion";
import { CircleXIcon } from "./icons.tsx";
import { EssenceIcon } from "./icons.tsx";
import { createTopUpSession, createGiftSession } from "../actions/stripe-actions.ts";
import { isExpoIOS } from "./platform-utils.ts";

export const TOP_UP_OPTIONS = [
  { price: 200, essence: 1100, popular: false, popularGift: false, id: "tu_xyh9uiondi4" },
  { price: 50, essence: 230, popular: true, popularGift: false, id: "tu_xnui32ho58u" },
  { price: 25, essence: 100, popular: false, popularGift: true, id: "tu_xyh9uiondi4" },
  { price: 10, essence: 38, popular: false, popularGift: false, id: "tu_xnui32ho58u" },
  { price: 5, essence: 18, popular: false, popularGift: false, id: "tu_xsainm1928u" },
];
interface TopUpOverlayProps {
  isOpen: boolean;
  onClose: () => void;
  channelId: string;
  isGift?: boolean;
  userName?: string;
}

export const TopUpOverlay: React.FC<TopUpOverlayProps> = ({ isOpen, onClose, channelId, isGift = false, userName }) => {
  const [loading, setLoading] = React.useState<number | null>(null);
  const [error, setError] = React.useState<string | null>(null);
  const [giftMode, setGiftMode] = React.useState(isGift);
  const [recipientEmail, setRecipientEmail] = React.useState("");
  const [senderName, setSenderName] = React.useState(userName || "");
  const defaultGiftMessage = `Awaken is like 24/7 spiritual coaching and has been amazing for me. Hope it helps you too!${userName ? ` - ${userName}` : ""}`;
  const [giftMessage, setGiftMessage] = React.useState(defaultGiftMessage);

  // Ref and handler for auto-selecting the default personal message only on the first focus
  const hasFocusedGiftMessage = useRef(false);
  const handleGiftMessageFocus = (e: React.FocusEvent<HTMLTextAreaElement>) => {
    if (!hasFocusedGiftMessage.current) {
      e.target.select();
      hasFocusedGiftMessage.current = true;
    }
  };

  const isIosExpo = isExpoIOS();

  // Reset gift mode when prop changes
  useEffect(() => {
    setGiftMode(isGift);
  }, [isGift]);

  // Add ESC key handler for accessibility
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };
    
    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [onClose]);

  // Lock body scroll when overlay is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
      return () => {
        document.body.style.overflow = '';
      };
    }
  }, [isOpen]);

  const handleTopUp = async (price: number, essence: number, topUpId: string) => {
    // Block payment for iOS Expo
    if (isIosExpo) {
      return;
    }

    try {
      setLoading(price);
      setError(null);

      let checkoutUrl: string | null = null;

      if (giftMode) {
        // Validate gift fields
        if (!recipientEmail) {
          setError("Please enter recipient's email address");
          setLoading(null);
          return;
        }
        
        // Create gift session (gift code will be generated server-side)
        console.log("[TOP-UP] Creating gift session with:", {
          channelId,
          recipientEmail,
          essence,
          price,
          giftMessage,
          senderName: senderName || "Anonymous"
        });
        
        checkoutUrl = await createGiftSession(
          channelId,
          recipientEmail,
          essence,
          price,
          giftMessage,
          senderName || "Anonymous"
        );
        
        console.log("[TOP-UP] Gift session URL:", checkoutUrl);
      } else {
        // Regular top-up
        checkoutUrl = await createTopUpSession(channelId, price, essence, topUpId);
      }

      if (checkoutUrl) {
        globalThis.location.href = checkoutUrl;
      } else {
        throw new Error("Failed to create checkout session");
      }
    } catch (err) {
      console.error("Top-up error:", err);
      setError(err instanceof Error ? err.message : "Failed to process");
    } finally {
      setLoading(null);
    }
  };

  if (!isOpen) return null;

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50"
      role="dialog"
      aria-modal="true"
      aria-label={giftMode ? "Send Essence Gift" : "Top Up Essence"}
    >
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: 10 }}
        transition={{ type: "spring", damping: 25, stiffness: 300 }}
        className="fixed inset-0 flex flex-col bg-[#1a1a1a]"
      >
        {/* Header with close button in natural flow */}
        <div className="flex justify-end p-4 sm:p-6" style={{ paddingTop: 'env(safe-area-inset-top, 1rem)' }}>
          <button
            onClick={onClose}
            className="p-2 rounded-full hover:bg-white/10 transition-colors"
            aria-label={giftMode ? "Close gift form" : "Close top-up"}
          >
            <CircleXIcon size={24} color="#ffffff" />
          </button>
        </div>

        {/* Scrollable content area */}
        <div 
          className="flex-1 overflow-y-auto overscroll-contain px-4 sm:px-8 pb-8"
          style={{ 
            WebkitOverflowScrolling: 'touch',
            paddingBottom: 'env(safe-area-inset-bottom, 2rem)'
          }}
        >
          <div className="w-full max-w-md mx-auto">
            {/* Title Section */}
            <div className="text-center mb-8">
              <div className="flex justify-center mb-4">
                <div className="text-[#ff6b35]">
                  <EssenceIcon size={48} color="#ff6b35" />
                </div>
              </div>
              <div className="space-y-2">
                <Text css="text-2xl font-bold text-white block">
                  {giftMode ? "Gift Awaken Essence" : "Top Up Essence"}
                </Text>
                <Text css="text-gray-400 block">
                  {giftMode ? "Share the gift of transformation" : "Power your transformative journey"}
                </Text>
              </div>
            </div>

            {/* Gift Mode Toggle */}
            {!isGift && (
              <div className="flex justify-center mb-6">
                <button
                  onClick={() => setGiftMode(!giftMode)}
                  className="flex items-center gap-2 px-4 py-2 rounded-full bg-white/10 hover:bg-white/20 transition-colors text-white text-sm"
                >
                  <span>🎁</span>
                  <span>{giftMode ? "Buy for myself" : "Send as gift"}</span>
                </button>
              </div>
            )}

            {/* Gift Form Fields */}
            {giftMode && (
              <div className="space-y-4 mb-6">
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">
                    Recipient Email <span className="text-red-400">*</span>
                  </label>
                  <input
                    type="email"
                    value={recipientEmail}
                    onChange={(e) => setRecipientEmail(e.target.value)}
                    placeholder="<EMAIL>"
                    className="w-full px-4 py-3 bg-black/50 border border-gray-700 rounded-lg text-white placeholder-gray-500 focus:border-[#FCA311] focus:outline-none transition-colors"
                    required
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">
                    Your Name
                  </label>
                  <input
                    type="text"
                    value={senderName}
                    onChange={(e) => setSenderName(e.target.value)}
                    placeholder="Your name (optional)"
                    className="w-full px-4 py-3 bg-black/50 border border-gray-700 rounded-lg text-white placeholder-gray-500 focus:border-[#FCA311] focus:outline-none transition-colors"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-400 mb-2">
                    Personal Message
                  </label>
                  <textarea
                    value={giftMessage}
                    onChange={(e) => setGiftMessage(e.target.value)}
                    onFocus={handleGiftMessageFocus}
                    placeholder="Add a personal message (optional)"
                    rows={3}
                    className="w-full px-4 py-3 bg-black/50 border border-gray-700 rounded-lg text-white placeholder-gray-500 focus:border-[#FCA311] focus:outline-none transition-colors resize-none"
                  />
                </div>
                {error && (
                  <div className="mt-4 p-3 bg-red-500/10 border border-red-500/20 rounded-lg text-red-500 text-sm text-center">
                    {error}
                  </div>
                )}
              </div>
            )}

            {/* iOS Expo Warning */}
            {isIosExpo && (
              <div className="mb-6 p-4 bg-[#FCA311]/20 border border-[#FCA311]/30 rounded-lg text-center">
                <Text css="text-[#FCA311] font-medium block">
                  {giftMode 
                    ? "Gifting essence isn't supported in this app. We know this isn't ideal."
                    : "Purchasing essence isn't supported in this app. We know this isn't ideal."}
                </Text>
              </div>
            )}

            {/* Options */}
            <div className="space-y-4">
            {TOP_UP_OPTIONS.map(({ essence, price, popular, popularGift, id }) => {
              const isPopular = giftMode ? popularGift : popular;
              return (
              <motion.div
                key={essence}
                whileHover={!isIosExpo ? { scale: 1.02 } : {}}
                className={`
                  relative p-4 rounded-lg border ${isIosExpo ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'}
                  ${isPopular 
                    ? 'border-[#FCA311] bg-gradient-to-b from-[#FCA31115] to-transparent' 
                    : 'border-gray-700 hover:border-[#FCA311]/50'}
                `}
                onClick={() => !isIosExpo && handleTopUp(price, essence, id)}
              >
                {isPopular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-[#FCA311] text-black text-xs font-bold px-3 py-1 rounded-full">
                      MOST POPULAR
                    </span>
                  </div>
                )}

                <div className="flex justify-between items-start">
                  <div className="space-y-1 flex flex-col">
                    <Text css="text-xl font-bold text-white block">{essence} Essence</Text>
                    <Text css="text-sm text-gray-400 block">{giftMode ? "Gift package" : ""}</Text>
                  </div>
                  {!isIosExpo && (
                    <div className="text-right flex flex-col">
                      <Text css="text-2xl font-bold text-[#FCA311] block">${price}</Text>
                      <Text css="text-sm text-gray-400 block">
                        {giftMode ? "Send Gift" : `$${(price/essence * 100).toFixed(2)}/100 essence`}
                      </Text>
                    </div>
                  )}
                </div>

                {loading === price && !isIosExpo && (
                  <div className="absolute inset-0 bg-black/50 rounded-lg flex items-center justify-center">
                    <div className="animate-spin rounded-full h-6 w-6 border-2 border-[#FCA311] border-t-transparent" />
                  </div>
                )}
              </motion.div>
            );})}
          </div>

          </div>
        </div>
      </motion.div>
    </motion.div>
  );
}; 