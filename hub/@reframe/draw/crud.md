```ts
import { createClient } from "https://db.reframe.so/@retune/leadgen@3.0.1";

const db = createClient({ url: Reframe.env.DATABASE_URL, token: sessionToken });

await db.users.create({
    name: "<PERSON>",
    email: "<EMAIL>",
});

await db.users
    .read({ email: "<EMAIL>" })
    .include({
        app: true,
    });

await db.users
    .update(
        { email: "<EMAIL>" },
        { name: "<PERSON>" },
    );

await db.users
    .delete({ name: "<PERSON>" });

await db.users
    .list({ name: "<PERSON>" })
    .limit(10)
    .offset(0)
    .sort("name", "asc");
```

```sql
-- # user

authenticated := auth.user.id is not null
admin         := 'db:admin' in auth.scope

-- create
admin OR auth.user.id = data.id

-- read
admin OR
auth.user.id = id

-- update
admin OR (
    auth.user.id = id AND
    -- users can not update their id, email
    data.id is null
    data.email is null
)

-- delete
   admin


-- # org

-- relations
members := member ON
    id = member.orgId

membership := member ON
    id = member.orgId AND
    member.userId = auth.user.id


-- fields
role :=
    if ownerId = auth.user.id then 'owner'
    else if membership is not null then membership.role
    else null

-- create
auth.user.id = data.ownerId

-- read
computed.role is not null

-- update
computed.role = 'owner' AND
data.id is null AND
data.ownerId is null

-- delete
computed.role = 'owner'


-- # member

org := org
    ON row.orgId = org.id

-- create
auth.user.id = row.org.ownerId
```
