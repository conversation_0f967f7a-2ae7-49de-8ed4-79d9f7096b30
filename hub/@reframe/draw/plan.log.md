# jul 15

[1200]

I will implement the algorithm now.

[1330]

Implemented the algorithm, there are a few areas that need to be addressed, like
instead of lane-first, we want to be task-first, so we can make sure the highest
priority task is worked on first.

[1530]

I have 19 minutes until standup, I will render project, details, assignee,
priority and estimate first, and after than increment/decrement priority and
estimate.

[1430]

I, again, have 19 minutes until dinner. In the meantime I'll increment /
decrement priority and estimate. After dinner, I will add select to select
project and assignee.

[1930]

I have 90 minutes until bed. Changing priority and estimate is done. I will now
implement deleting tasks, and deleting edges.
