### User stories

<PERSON><PERSON><PERSON> goes to db.reframe.so, and sees a list of all his databases.

There is a `create database` button, which can create a new empty database. A
database can be created from scratch, a schema, or forked from any snapshot of
another database (with or without data).

When <PERSON><PERSON><PERSON> clicks on a database, he goes to the database manager for that
database.

There is also a `create connection` button, which can connect to a database with
a url, even if it's not in the list, and then he can use that database similarly
to his own databases (except for deleting it).

When <PERSON><PERSON><PERSON> enters a database, he can run sql queries, view all tables in the
database, and create new tables.

When he clicks on any table on the sidebar, it shows the rows in the table.

He can also create new columns, indices and rows in the table.

He can also run queries in the database in a console.

Finally, he can `apply` any database schema to another database.

---

1. create a app `@template/blank`
2. copy the `@template/blank` to `@reframe/db`
3. `/` should show all tables of the database at `libsql://localhost:8080?tls=0`
   - https://kysely.dev/docs/recipes/introspecting-relation-metadata
4. `/table/:table` should show all columns of the table and index
5. `/table/:table/create/column` should show inputs to create a new column
6. `/table/:table/create/index` should show inputs to create a new index
7. `/console` should show a console to run SQL queries
