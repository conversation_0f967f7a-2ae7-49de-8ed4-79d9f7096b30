## What is client and server boundary?

All componenet starts rendering on the server.

```tsx
// /path/to/app.tsx
import Counter from "./counter.tsx";

export const App = () => {
  return (
    <div>
      <h1>Hello World</h1>
      <Counter />
    </div>
  );
};

// /path/to/counter.tsx
export const Counter = () => {
  const [count, setCount] = useState(0);

  return (
    <div>
      <h2>{count}</h2>
      <button onClick={() => setCount(count + 1)}>Increment</button>
    </div>
  );
};
```

The code above will render the html, but on the client side, it will not be
interactive. Because the server will simply render the html, and send it to the
client.

To make it interactive, we need to ship the code of `Counter` to the client, and
need to tell react how to find the `Counter` component on the client.

```tsx
// /path/to/app.tsx
import Counter from "./counter.tsx";

// This tells react that Counter is a client side component, and the $$id should be something that can be used to load the component on the client
Counter.$$typeof = Symbol.for("react.client.reference");
Counter.$$id = "/path/to/counter.tsx#Counter";

export const App = () => {
  return (
    ...
  );
};
```

Now, imagine the `Counter` component also updates the counter in the database.

```tsx
// /path/to/counter.tsx

import { increment } from "./db.ts";

export const Counter = () => {
  const [count, setCount] = useState(0);

  return (
    <div>
      <h2>{count}</h2>
      <button
        onClick={() => {
          setCount(count + 1);
          increment();
        }}
      >
        Increment
      </button>
    </div>
  );
};

// /path/to/db.ts

export const increment = async () => {
  const db = await getDb();
  return db.counter.update({ count: { $inc: 1 } });
};
```

Now, this operation will fail, because the `db` is not available on the client.
What we really want to do is request the server to perform the operation.

```tsx
// /path/to/db.client.ts

export const increment = async (...args) => {
  return fetch("/_special_server_action_endpoint", {
    method: "POST",
    body: JSON.stringify({
      id: "/path/to/db.ts#increment",
      args,
    }).then((res) => res.json());
};

// path/to/server.ts

export default (request) => {
    if (request.url === "/_special_server_action_endpoint") {
        const { id, args } = JSON.parse(await request.text());
        const [path, method] = id.split("#");
        const module = await import(path);
        const result = await module[method](...args);
        return new Response(JSON.stringify(result));
    }
}

// /path/to/counter.tsx

import { increment } from "./db.client.ts";

export const Counter = () => {
  const [count, setCount] = useState(0);

  return (
    <div>
      <h2>{count}</h2>
      <button
        onClick={() => {
          setCount(count + 1);
          increment();
        }}
      >
        Increment
      </button>
    </div>
  );
};
```

---

Here's what happens when we import a module with `with { use: "client" }`.

```tsx
// import _ from "/path/to/counters.tsx";

import { increment } from "./db.ts";

export const Counter = () => {
  const [count, setCount] = useState(0);

  return (
    <div>
      <h2>{count}</h2>
      <button
        onClick={() => {
          setCount(count + 1);
          increment();
        }}
      >
        Increment
      </button>
    </div>
  );
};

// import _ from "/path/to/counter.tsx" with { use: "client" };

import * as imports from "/path/to/counters.tsx";

const createClientProxy = (module) => {
  module.$$typeof = Symbol.for("react.client.reference");
  module.$$id = "/path/to/counters.tsx#Counter";
  return module;
};

export const Counter = createClientProxy(imports.Counter);
```

Similarly, if with wanted to implement something like `with { use: "server" }`,
we could do something like this,

```tsx
// import _ from "/path/to/db.ts";

export const increment = async () => {
  const db = await getDb();
  return db.counter.update({ count: { $inc: 1 } });
};

// import _ from "/path/to/db.ts" with { use: "server" }

const createServerProxy = (name) => {
  return {
    $$typeof: Symbol.for("react.server.reference"),
    $$id: `/path/to/db.ts#${name}`,
  };
};

export const increment = createServerProxy("increment");
```
