```tsx
// @/app.tsx
import { createAuth } from "@reframe/auth/auth.ts";

const Auth = createAuth({
  // ...
});

<Router.Route
  middleware={Auth.middleware}
  /**
   * if you have multiple middlewares, you can use an array
   * middleware={[Auth.middleware, async (request, next) => { ... }]}
   */
  layout={(Router) => (
    <Auth.Provider>
      <Router.Layout />
    </Auth.Provider>
  )}
  route:app={(Router) => (
    <Router.Route
      page={() => (
        <Auth
          use={(auth) => <Router.page auth={auth} />}
        />
      )}
    />
  )}
/>;

// @/client.tsx
import { Auth } from "@reframe/auth/auth.ts";

export const Component = () => {
  return (
    <Auth
      use={(auth) => (
        <Show
          when={auth.authenticated}
          then={() => (
            <Y>
              <X>{JSON.stringify(auth.user)}</X>
              <Button onClick={Auth.signOut}>log out</Button>
            </Y>
          )}
          else={() => (
            <Y>
              <Button onClick={() => Auth.signIn("google")}>
                login with google
              </Button>
            </Y>
          )}
        />
      )}
    />
  );
};

// @/action.ts

import { getAuth } from "@reframe/auth/auth.ts";

export const getSession = async () => {
  const auth = await getAuth();

  if (auth.authenticated) {
    return auth.session;
  }

  throw new Error("not authenticated");
};
```
