# Goal

Implement `deno task build @org/app/path/to/entry.ts` and
`deno task deploy @org/app/path/to/entry.ts`.

## Build

`build` will create a directory `.build/@org/app/` and put all the necessary
files to run `/path/to/entry.ts` inside `.build/@org/app/src`.

If implementing `build` is successful, one should be able to do the following,

```sh
deno task build @gates/six-new/main.tsx
cd .build/@gates/six-new
deno run --allow-all entry.ts
# should run the app
```

## Deploy

`deploy` will take an existing build directory, deploy it to Deno Deploy, and
share the url.

If implementing `deploy` is successful, one should be able to do the following,

```sh
deno task build @gates/six-new/main.tsx
deno task deploy @gates/six-new/main.tsx
# should deploy the app and share the deploy url
```

## Implementation Details

First, let's take a look at what happens when we run
`deno task watch @org/app/path/to/entry.ts`, or for a specific example,
`deno task watch @gates/six-new/main.tsx`.

We have a `deno.json` file in the root of the project that looks like this,

```json
// deno.json
{
  "tasks": {
    "watch": "cd hub; deno run --allow-all --watch --unstable-kv @reframe/zero/run.ts --watch=true",
    "run": "cd hub; deno run --allow-all --unstable-kv @reframe/zero/run.ts"
  }
}
```

Note that the `watch` and `run` tasks are the same, except for the `--watch`,
for now, let's ignore `watch` and focus on `run`.

When we run `deno task run @gates/six-new/main.tsx`, it will run the following
command,

```sh
deno run --allow-all --unstable-kv @reframe/zero/run.ts @gates/six-new/main.tsx
```

This runs the script at `@reframe/zero/run.ts` with the argument (`Deno.args`)
of `["@gates/six-new/main.tsx"]`.

The in the `run.ts` script, we have the following,

```ts
// get the database
const db = ...

// sync all changes in local files (updated recently) with the database
await push(...)

// compute is a FS that takes a path (eg: /main.tsx), and `computes` the latest content of the path
const createCompute = ...

// blob is a FS that takes a hash, and returns the content of the hash. note that, by design, the content of any hash is immutable and never changes
const createBlob = ...


// now we create a runtime, more on what a runtime does is later
const runtime = createRuntime(
    Deno.args, // ["@<org>/<app>/<path/to/entry.ts>"]
    createCompute,
    createBlob
)

// load the commit json, if exists, from .cache/commit/--@<org>--<app>--<path-to-entry>.json
// a `commit` contains a map between each path and its hash, along with a few other metadata
await runtime.sourceGraph.load();

// import the entry file, but not the /~@/ part - we will describe what this means later
await runtime.import("/~@/@gates/six-new/main.tsx")
```

What does it take to run `@gates/six-new/main.tsx`, in the beginning when we are
running it for the first time?

1. We need to `compute` the source of `@gates/six-new/main.tsx`, which we can by
   calling `createCompute().read("/@gates/six-new/main.tsx")`.
2. Then, we need to compute all other path that `@gates/six-new/main.tsx`
   imports, recursively.
3. Now that we have all the source, we can start `evaluating` them, starting the
   `leaf` module (which doesn't import any other module), and then moving up the
   tree, and finally evaluating `@gates/six-new/main.tsx`.

Now, note that, we can repeat the same steps every time we want to run the
`@gates/six-new/main.tsx`. But most of the computation is redundant on
subsequent runs, and we can cache the results.

This is where the `commit` comes in. A `commit` is a json file that contains a
map between each path and its hash. This way, when we run the same command
again, we can skip the `compute` step for all the paths.

And if something changes, we can recompute only the paths that are affected by
the changes, an update the commit.

_This means_, if you have a commit json file, that contains the entry you want
to run, you don't need to do _any_ computation, and you can directly run the
entry.

## Implementing `build`

Similar to `@reframe/zero/run.ts`, create a `@reframe/zero/build.ts` that takes
the same arguments as `run.ts`, but instead of running the entry, it `builds`
the entry - whatever build means for now - without running it.

Update `deno.json` to have a `build` task that runs `@reframe/zero/build.ts`
with the same arguments as `run.ts`.

Complete the implementation of `@reframe/zero/build.ts`.
