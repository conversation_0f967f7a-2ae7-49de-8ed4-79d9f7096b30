### async

we should be able to do something like this,

```ts
import { someFunction } from "@reframe/search" with { loader: "async" };

const Workflow = (event) => (
  <Step
    retry={3}
    name="query-vectordb"
    run={async () => {
      const embedding = createEmedding(event.data.input);
      return await index.query({
        vector: embedding,
        topK: 3,
      }).matches;
    }}
    next={(similar) => (
      <Step
        name="generate-llm-response"
        run={async () =>
          await llm.createCompletion({
            model: "gpt-3.5-turbo",
            prompt: createPromptForSummary(similar),
          })}
        next={(data) => (
          <Wait
            for="event"
            event="approval"
            timeout={1000 * 60 * 60 * 24}
            next={(event) => (
              <Step
                sleep={1000 * 60 * 60 * 24}
                name="save-to-db"
                run={async () => {
                  await db.summaries.create({
                    requestID: event.data.requestID,
                    data,
                  });
                }}
              />
            )}
          />
        )}
      />
    )}
  />
);
```
