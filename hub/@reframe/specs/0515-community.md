## user journey

- onboarding
  - logs into retune
  - retune sends to `/auth?token=...` with a token
  - `/auth` verifies the token, upserts the user, sets session, and redirects to
    `/join`
  - `/join` checks if there's a user in the session, if not, shows a
    `login with retune` button that redirects to sign in with retune
  - `/join` checks if the user has access to the community, if so, redirects to
    `/channel`
  - `/join`, if the user doesn't have access, checks if they have already
    requested access, if so, redirects to `/join/thanks`
  - `/join`, if the user doesn't have access and hasn't requested access, shows
    a form to request access
  - the form accepts a display name, and initial message, and on submit, saves
    them and sends to the next page, then there are a few steps and after the
    final step, the user is redirected to `/join/thanks`
- channel
  - `/channel` checks if the user has access, if not, redirects to `/join`
  - `/channel` lists all the channels the user has access to, and when a channel
    is clicked, it redirects to `/channel/:channelId`
  - `/channel/:channelId` checks if the user has access to the channel, if not,
    redirects to `/channel`
  - `/channel/:channelId` shows the channel, fetches all recent messages, and
    shows them
  - `/channel/:channelId` has a form to send a message, and when a message is
    sent, it sends the message to the server, and confirms the message is sent.

## Tickets

More will be added later, implement each of these tickets in order, one by one,
in separate branches and create a PR for each ticket.

- **Wed, May 15**
  - [CM-001] Implement `/auth?token=token`, where, initially, the `token` is the
    email of the user.
    - Steps
      - upsert the user with the email
      - set `JSON.stringify({ email: email })` in the session
      - redirect to `/join`
      - in the `/join` page, show a read-only input with the email before the
        input for display name.
    - Success criteria
      - `join` page should be able to read the email from the session, and
        render it in the input field.
  - [CM-002] After the user enters the display name and intro, save them in the
    database, and redirect to `/join/thanks`.
    - Steps
      - create a new migration to add a boolean field `approved` with default
        value `false` in the user table
        (`./db/migrations/01-add-user-approved.ts`)
      - trigger a server action when user clicks save
      - in the action, save display name as `username` and intro as
        `metadata: { intro }` in the user table
      - redirect to `/join/thanks`
    - Success criteria
      - the user should be able to save the display name and intro.
      - the user should be redirected to `/join/thanks` after saving.
      - if the user goes to `/join` again, they should see the display name and
        intro they saved, but they will be read-only and the button should say
        "Requested access" and be disabled.
- **Thu, May 16**
  - [CM-003] List all channels from database
    - Steps
      - create a new migration to seed #announcements, #intro, #retune channels
        (`./db/migrations/02-seed-channels.ts`)
      - instead of hardcoding the channels in the `/channel` page, fetch them
        from the database
      - for now, all approved users will have access to all channels
    - Success criteria
      - the `/channel` page should list all the channels from the database
  - [CM-004] Show messages in the channel
    - Steps
      - create a new migration to seed messages in the #announcements, #intro
        and #retune channel (`./db/migrations/03-seed-messages.ts`)
      - instead of hardcoding the messages in the `/channel/:channelId` page,
        fetch them from the database
    - Success criteria
      - the `/channel/:channelId` page should show all the messages from the
        database
  - [CM-005] Send message in the channel
    - Steps
      - When send button is clicked, trigger a server action to save the message
        in the database, and on success, update the state to show the new
        message and clear the input field.
    - Success criteria
      - the message should be saved in the database (available in the next
        reload)
      - the message should be shown in the channel
      - the input field should be cleared after sending the message
      - the input field should be disabled while the message is being sent
  - [CM-006] Implement reply
    - Steps
      - When reply button is clicked, the subsequent message should be a reply
        to the message
    - Success criteria
      - the message should be saved in the database as a reply to the message
      - the message should be shown as a reply in the channel
      - the input field should show an indicator that the message is a reply
- **Fri, May 17**
  - [CM-007] Implement reactions
    - Steps
      - Show a list of reactions under each message, only when they are non-zero
      - Add a json field `reactions` in the message table with default value
        `{}` (`./db/migrations/04-add-reactions.ts`)
      - When a reaction is clicked, trigger a server action to toggle the
        reaction for the user in the reaction table, and update the reactions
        count in the message table
      - When a reaction is clicked again, trigger a server action to remove the
        reaction
    - Success criteria
      - a user should be able to react to a message
      - each reaction should be counted and shown under the message
      - a user should be able to remove their reaction
