```tsx HKT
interface HKT {
  readonly _A?: unknown;
  readonly type?: unknown;
}

type Kind<F extends HKT, A> = F extends {
  readonly type: unknown;
} ? (F & {
    readonly _A: A;
  })["type"]
  // F is generic, we need to mention all of the type parameters
  // to guarantee that they are never excluded from type inference
  : ({
    readonly _F: F;
    readonly _A: () => F;
  });

interface TypeClass<F extends HKT> {
  readonly _F?: F;
}

// ArrayHKT
interface ArrayHKT extends HKT {
  readonly type: Array<this["_A"]>;
}

interface SchemaHKT extends HKT {
  readonly type: Schema<this["_A"]>;
}

type Assume<T, U> = T extends U ? T : U;

interface Mappable<M extends HKT> extends TypeClass<M> {
  readonly map: <A, B>(ma: Kind<M, A>, f: (a: A) => B) => Kind<M, B>;
}

type M = Mappable<SchemaHKT>["map"];
```

```tsx
<Playground fn={someFn} />;

const Playground = ({ fn }) => {
  const input = type(fn);
  /**
   * during compilation, this will be
   */
};

const j = t
.def('a', t.object({ b: t.ref('b') }))
.def('b', t.object({ a: t.ref('a') }))
.def(
  'json',
    t.union([
    t.string(),
    t.number(),
    t.boolean(),
    t.null(),
    t.array(t.ref('json')),
    t.record(t.string(), t.ref('json')),
  ]);
).compile(t.object({
  json: t.ref('json'),
  a: t.ref('a'),
  b: t.ref('b'),
}));
```

```tsx
```

```tsx shapes
<Form
  schema={(t) =>
    t.object({
      name: t.object({
        first: t.string(),
        last: t.string(),
      }),
      age: t.number(),
      experience: t.array(t.object({
        name: t.string(),
        duration: t.number(),
      })),
      availability: t.record(
        t.string(),
        t.discriminated("type", [
          {
            type: z.literal("weekday"),
            weekday: t.tuple(
              t.number(),
              t.number(),
              t.number(),
              t.number(),
              t.number(),
            ),
          },
          {
            type: z.literal("weekend"),
            weekend: t.tuple(t.number(), t.number()),
          },
        ]),
      ),
      gender: t.union([
        t.literal("male"),
        t.literal("female"),
      ]),
      employed: t.boolean(),
    })}
  use={(form) => (
    <>
      <form.Field path={['name', 'path']} />
      <form.Field name="name.last" />
      <form.Field path={['age', 0]} />
      <form.Field
        name="experience"
        use={(item, index) => (
          <X>
            <Y>
              <item.Field name="name" />
              <item.Field name="duration" />
            </Y>
            <X>
              <item.MoveUp />
              <item.MoveDown />
              <item.Remove />
            </X>
            <Show if={item.last} then={<item.InsertAfter />} />
          </X>
        )}
      />
      <form.Field
        name="availability"
        use={(entry, key) => (
          <Y>
            <X>
              <entry.Key />

              <entry.Field
                on:weekday={(option) => (
                  <X>
                    <option.Field name="type" />

                    <option.Field name="weekday[0]" />
                    <option.Field name="weekday[1]" />
                    <option.Field name="weekday[2]" />
                    <option.Field name="weekday[3]" />
                    <option.Field name="weekday[4]" />
                  </X>
                )}
                on:weekend={(weekend) => (
                  <X>
                    <option.Field name="type" />

                    <option.Field name="weekend[0]" />
                    <option.Field name="weekend[1]" />
                  </X>
                )}
              />
            </X>

            <X>
              <entry.InsertBefore />
              <entry.MoveUp />
              <entry.MoveDown />
              <entry.Remove />
              <entry.InsertAfter />
            </X>
          </Y>
        )}
      />

      <form.Field name="gender" />
      <form.Field name="employed" />

      <form.Field name="employed" use={([employed, setEmployed]) =>
        <Show
          when={employed}
          then={() => <form.Field name="job" />}
        />
      }>

      <Show
        if={form.value.employed}
        then={<form.Field name="job" />}
      />
    </>
  )}
/>;
```

```tsx form value
number -> number
string -> string
boolean -> boolean
null -> null
array -> array
union ->
```

- form
  - initialize values
  - render values

- union .or
- form
- optional / nullable
- extensions
- templates
