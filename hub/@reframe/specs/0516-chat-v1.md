# v1

# Schema

```ts
// authentication service

type Organization = {
  id: string;
  slug: string;
  name: string;
  timestamp: number;
};

type User = {
  id: string;
  email: string;
  username?: string;
  phone?: string;
  name?: string;
  avatar?: string;
  timestamp: number;
};

type Member = {
  orgId: string;
  userId: string;
  roleId: string;
  timestamp: number;
};

type Role = {
  id: string;
  orgId: string;
  name: string;
  timestamp: number;
};

type Permission = {
  id: string;
  name: string;
  timestamp: number;
};

type RolePermission = {
  roleId: string;
  permissionId: string;
  timestamp: number;
};

type Auth = {};

// chat service

type Space = {
  id: string;
  ownerId: string;
  name: string;
  timestamp: number;
};

type Session = {
  id: string;
  spaceId: string;
  ownerId: string;
  timestamp: number;
};

type Message = {
  id: string;
  sessionId: string;
  senderId: string;
  content: string;
  timestamp: number;
};
```

```ts
type User = {
  id: string;
  role: "admin" | "user" | "agent" | "system";
  name?: string;
  email?: string;
  avatar?: string;
  timestamp: number;
  version: number;
};

type AgentTemplate = {
  id: string;
  schema: AgentSchema;
  definition: AgentDefinition;
  timestamp: number;
};

type Agent<T> = {
  id: string;
  name: string;
  ownerId: string;
  metadata: T;
  timestamp: number;
};

type Session<T> = {
  id: string;
  appId: string;
  ownerId: string;
  timestamp: number;
};

type Participant = {
  threadId: string;
  userId: string;
  timestamp: number;
};

type Message = {
  id: string;
  threadId: string;
  senderId: string;
  content: string;
  timestamp: number;
};
```

# Tickets

- **Thu, May 16**
  - [CT-000] setup
    - Steps
      - [follow this doc](../docs/0516-starting-a-new-app.md) to create a new
        app `@script-company/chat`
    - Success criteria
      - the app is accessible on `http://localhost:8000`
      - all the routes are working
  - [CT-001] implement initial ui
    - Steps
    - Success criteria
      - the chat ui is implemented on `/t/:threadId` with a few hard-coded
        messages
      - on sending a message, the message should be shown in the chat ui (client
        only)
  - [CT-002] implement sending messages
    - Steps
      - delete your app db
      - create tables `user`, `thread`, `participant`, `message`
      - run the migration to create the tables
      - from db shell, insert a user with id `test-user-1` with role `user`, a
        thread and a app.
      - create a server action to send messages
    - Success criteria
      - the user can send messages and it will be saved in the database, and
        shown in the chat ui for the thread
      - ownerId / senderId is hardcoded to `test-user-1` for this ticket
- **Fri, May 17**
  - [CT-003] respond with ai
    - Steps
      - create new app `@reframe/ai`, and copy
      - the server action should respond with an ai message as a readable stream
      - render the ai message in the chat ui
      - from db shell, insert a user with id `ai`
      - save the ai message in the database on completion
    - Success criteria
      - the user should see the stream of ai messages in the chat ui
