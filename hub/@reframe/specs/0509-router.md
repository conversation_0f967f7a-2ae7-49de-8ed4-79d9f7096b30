### Design goal

/chat/1/(route=room)/(w1=hello,w2=world)
/chat/1/(route=room)/(x/y)/(w1=hello/world/(a=2,b=3),w2=world)

--

next steps

- load layout and components in parallel
- update Link in the client to only load the updated layout and components, and
  cache all segments in the client
- implement Router.redirect() / <Router.Redirect />
- support group layouts (eg: auth / admin)
- support slots and parallel routes
