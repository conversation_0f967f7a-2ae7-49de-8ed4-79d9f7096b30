If we don't use JSX, it already works with custom pragma.

```tsx
/** @jsx h */

type Component<
  P,
  <PERSON> extends Element<any, any[], any>,
> = (props: P) => E;

type Node = Element<any, any[], any> | string | number;

type Element<
  Props,
  Children extends Node[],
  Tag extends Component<Props & { children: Children }, any> | string,
> = {
  tag: Tag;
  props: Props;
  children: Children;
};

const h = <
  Props,
  Children extends Node[],
  Tag extends
    | Component<
      Props & {
        children: Children;
      },
      any
    >
    | string,
>(tag: Tag, props: Props, ...children: Children) => {
  return {
    tag,
    props,
    children,
  } as Element<Props, Children, Tag>;
};

const S = <A,>({ id, value }: {
  id: string;
  value: A;
}) => {
  return h("span", { id }, JSON.stringify(value));
};

const X = <A, B>({ a, b }: {
  a: A;
  b: B;
}) => {
  return h(
    "div",
    { id: "foo" },
    h(S, { id: "bar", value: a }),
    h(S, { id: "baz", value: b }),
  );
};

const a = h("div", { id: "foo" }, "Hello");
const b = h(
  X<string, { z: number }>,
  { a: "yo", b: { z: 20 } },
  "World",
  h(X, { a: "aha", b: { zz: 20 } }, "!!!", h("span", { id: "quux" }, "???")),
);

console.log(b);
```

One solution in our editor could be to transform the code from TSX -> TS, and
use that for typechecking instead of the original TS code.

Or, alternatively, we could do the TSX -> [JS, DTS] transformation, and use the
and further transform the DTS to use the custom pragma.
