```tsx
/**
 * { path: "/start", id: 1 }
 * <- user joins
 * wait 1s
 * -> "Hey I am dj, founder of retune!"
 * -> "Are you looking to build a chatbot or sell chatbots to your clients?"
 * { path: "/intent", id: 2 }
 * ~~> schedule { path: "/start/followup", id: 2 } in 10s
 *
 * # scenario 1
 * wait 10s, no response
 * -> "/start/followup" { id: 2 }
 * { path: "/start/followup", id: 3 }
 *
 * # scenario 2
 * after 5s
 * <- I want to build a chatbot for my website
 * { path: "/build", id: 3 }
 * after 5s
 * <- { path: "/followup", id: 2 } -> reject
 */

// const Send = (props) => (
//   <Reframe.Block
//     act={async (ctx) => {
//       await ctx.db.insert(uuid(), props.content);
//     }}
//     edit={(self) => (
//       return <Input value={props.content} onChange={(e) => self.update(e.target.value)} />
//     )}
//   />
// );

((ctx) => (
  <Route
    start={
      <Route
        match={
          <>
            <Wait delay={1000} />
            <Send content="Hey I am dj, founder of retune!" />
            <Send content="Are you looking to build a chatbot or sell chatbots to your clients?" />
            <Next to="/intent" timeout={10000} />
          </>
        }
        followup={
          <>
            <Send content="I am always here to help if you need me for anything" />
            <Listen to="/intent" timeout={10000} onTimeout="/intent" />
          </>
        }
      />
    }
    intent={
      <If
        condition={ctx.self}
        then={
          <>
            <Send content="I am waiting!" />
            <Listen to="/intent" timeout={10000} onTimeout="/intent" />
          </>
        }
        otherwise={
          <Flow>
            <AI.Intent
              model="gpt-4"
              messages={
                <>
                  <Message.System content="does the user wants to build a chatbot, sell a chatbot or something else?" />
                  <ctx.Messages />
                </>
              }
              intent:build={
                <Intent
                  when="user wants to build a chatbot"
                  next={<Redirect to="build" />}
                />
              }
              intent:sell={
                <Intent
                  when="user wants to sell a chatbot"
                  next={<Redirect to="build" />}
                />
              }
              otherwise={(intent) => (
                <AI
                  model="gpt-4"
                  messages={
                    <>
                      <Message.System content="answer the user's question, and ask follow up question and entice them to share their email so our team can help them" />
                      <ctx.Messages />
                    </>
                  }
                  next={(response) => (
                    <Flow>
                      <Send content={response} />
                      <Listen timeout={10000} to="/email" />
                    </Flow>
                  )}
                />
              )}
            />
          </Flow>
        }
      />
    }
    email={(ctx) => (
      <Flow>
        <AI.Action
          messages={
            <>
              <Message.System content="thanks for sharing your email, our team will reach out to you soon!" />
              <ctx.Messages />
            </>
          }
          action:email={
            <Action
              description="extract email if the user shared"
              property:email={
                <Action.Property
                  type="string"
                  description="email of the user"
                />
              }
            />
          }
          next={<Redirect to="thanks" />}
        />
      </Flow>
    )}
  />
));
```

```tsx
const Hello = createContext<{ hello: string }>();
// nested providers
const Example1 = () => {
  /**
   * renders:
   * <div>world</div>
   * <div>earth</div>
   * <div>universe</div>
   */
  return (
    <Hello.Provider value={{ hello: "world" }}>
      <Hello.Consumer use={(value) => <div>{value.hello}</div>} />

      <Hello.Provider value={{ hello: "earth" }}>
        <Hello.Consumer use={(value) => <div>{value.hello}</div>} />
      </Hello.Provider>

      <Hello.Provider value={{ hello: "universe" }}>
        <Hello.Consumer use={(value) => <div>{value.hello}</div>} />
      </Hello.Provider>
    </Hello.Provider>
  );
};

// multiple providers
const Request = createContext<{ request: Request }>();
const Headers = createContext<{ headers: Headers }>();
const Cookies = createContext<{ cookies: Record<string, string> }>();

const Example2 = () => {
  /**
   * renders:
   * <div>GET /</div>
   * <div>Content-Type: text/html</div>
   * <div>Cookie: session=123</div>
   */
  return (
    <Request.Provider value={{ request: new Request("/") }}>
      <Headers.Provider
        value={{ headers: new Headers({ "Content-Type": "text/html" }) }}
      >
        <Cookies.Provider value={{ cookies: { session: "123" } }}>
          <Request.Consumer
            use={(value) => (
              <div>{value.request.method} {value.request.url}</div>
            )}
          />

          <Headers.Consumer
            use={(value) => <div>{value.headers.get("Content-Type")}</div>}
          />

          <Cookies.Consumer
            use={(value) => <div>{value.cookies["session"]}</div>}
          />
        </Cookies.Provider>
      </Headers.Provider>
    </Request.Provider>
  );
};

// put anything in the provider!
import Message from "./Message";

const getMessages = async () => {
  await new Promise((resolve) => setTimeout(resolve, 1000));
  return [{ role: "user", content: "hello" }, {
    role: "assistant",
    content: "hi",
  }];
};

const Messages = createContext<
  { messages: Promise<{ role: string; content: string }[]> }
>();

const MDX = createContext<
  Record<string, React.ComponentType<{ children: React.ReactNode }>>
>();

const Example3 = () => {
  /**
   * renders:
   * <div>world</div>
   * <div>earth</div>
   * <div>universe</div>
   */
  return (
    <Messages.Provider value={{ messages: getMessages() }}>
      <MDX.Provider
        value={{
          h1: ({ children }) => <h1>{children}</h1>,
          p: ({ children }) => <p>{children}</p>,
        }}
      >
        <Chat />
      </MDX.Provider>
    </Messages.Provider>
  );
};

const Chat = () => {
  return (
    <MDX.Consumer
      use={({ h1: H1, p: P }) => {
        return (
          <Suspense>
            <Messages.Consumer
              use={async (messagesPromise) => {
                const messages = await messagesPromise;

                return messages.map(({ role, content }) => (
                  <div>
                    <H1>{role}</H1>
                    <P>{content}</P>
                  </div>
                ));
              }}
            />
          </Suspense>
        );
      }}
    />
  );
};
```
