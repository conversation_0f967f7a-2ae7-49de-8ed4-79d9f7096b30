```tsx
// retune

const Reframe = <
   Props,
   Next
>({ src, next, ...props }: Props & {
   src: string,
   next: (props: Next) => React.ReactElement
}) => {
   const prevWebpackRequire = globalThis.__webpack_require__;
   const rsc = await fetch(src, {
       method: 'GET',
       body: serialize(props),
   };

   globalThis.__webpack_require__ = (module: string) => {
       // this ensures webpack require doesn't compete with other <Reframe> components
       if (module === rsc.headers.get('$$next')) {
           return new Proxy({}, {
               get: (target, prop) => {
                   return next;
               },
           });
       }

       return prevWebpackRequire(module);
   };

   return <Suspense>
       {createFromReadableStream(rsc.body, {})}
   </Suspense>
}

const Something = async () => {
   const users = await db.getUsers();

   return (
       <X>
           <Reframe<{
               users: User[];
               settings: Settings;
           }, {
               messages: Message[];
           }>
               src="https://retune.reframe.so/@retune/community/app.tsx"
               users={users}
               settings={settings}
               next={props => <Inner {...props} />}
           />
       </X>
   );
}
```
