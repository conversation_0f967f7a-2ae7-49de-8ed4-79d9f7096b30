# to string

```tsx dom nodes and properties
render(
  <div />,
); // => "<div></div>"

render(
  <div>
    <span />
  </div>,
); // => "<div><span></span></div>"

render(
  <div hello="world" />,
); // => "<div hello="world"></div>"

render(
  <div>text</div>,
); // => "<div>text</div>"
```

```tsx definitions
render(
  <const
    value="hello"
    use={(name) => (
      <div>
        <span>{name}</span>
        <button>save {name}</button>
      </div>
    )}
  />,
); /* =>
<>
  <!-- const ->
    <script data-role="const">
      register(value, "hello");
    </script>
    <div>
      <span>hello</span>
    </div>
  <!-- /const ->
</>
*/

render(
  <const
    value={
      <div>
        <const
          value="world"
          use={(name) => (
            <span>
              {name}
            </span>
          )}
        />
      </div>
    }
    use={(element) => (
      <div>
        {element}
      </div>
    )}
  />,
); /* =>
<>
  <!-- const ->
    <div>
      <div>
        <
      </div>
    </div>
  <!-- /const ->
</>
*/
```

```tsx async
const getData = async () => {
  // wait for 1 second
  await new Promise((resolve) => setTimeout(resolve, 1000));
  return { x: 1, y: 2 };
};

render(
  <async
    value={() => getData()}
    // message: Async<{ x: number, y: number }>
    // message.x -> Async<number>
    use={(message) => (
      <div>
        <div>
          <suspense>
            {message.x}
          </suspense>
        </div>
        <div>{message.y}</div>
      </div>
    )}
  />,
); /* =>

<div>
  <div>
    <script>
  </div>
  <div>
    <script>
  </div>
</div>
*/
```

```tsx
<db.Select 
  from="apps"
  where={{id: '1'}}
  use={app => (
    <div>
      <span>{app.name}</span>
    </div>
  )}
/>

<window
  use={(window) => (
    <div>
      <span>Hello {window.location.href}</span>
    </div>
  )}
/>

<Encrypt
  $={db.Select}
  from="apps"
  where={{id '1'}}
  use={app => (
    <div>
      <span>{app.name}</span>
    </div>
  )}
/>
```

```tsx
type Value<T extends Serializable> =
  | { kind: "const"; value: T }
  | { kind: "async"; value: Async<T> };
  | { kind: "state"; value: State<T> };
  | { kind: "window"; value: Window<T> }
  | { kind: "secret"; value: Secret<T> };


type Async<T> = {
  value: () => Promise<T>;
  use: <U>(fn: (value: T) => U) => Async<U>;
};

type State<T> = {
  value: () => Promise<T>;
  use: <U>(fn: (value: T) => U) => State<U>;
};

const x = async(() => setTimeout(() => 1, 1000));
const y = x.use((x) => { x: x, x2: [x*x], a: Array(x).fill(0) }) // Async<{ x: number, x2: number }>
const z = y.x2; // y.use((x) => x.x2) -> Async<[number]>
const a = y.a; // y.use((x) => x.a) -> Async<number[]>
const b = y.a[0].c; // y.use((x) => x.a) -> Async<undefined>

const [sx, setSx] = state(10) // [State<number>, SetState<number>]
const sy = sx.use((x) => ({y: x + 1})) // State<{y:number}>
const sz = sy.y // sy.use((x) => x.y) -> State<number>

const x2 = state(x); // State<Async<number>>
const y2 = x2.use(
  (x: Async<number>) => x.use(
    (x: number) => ({ x: x, x2: [x*x], a: Array(x).fill(0) }) 
  )
); // Compute<Async<{ x: number, x2: number }>>
const z2 = y2.x2;
  // y2.use(x => x.use(x => x.x2)) -> Compute<Async<[number]>>


render("div", { x: '1' }): Stream<DOM>
controller.enqueue(`<div x="1"></div>`)

render("div", { x: async(1) }, children): Stream<DOM>
const id = "0";
controller.enqueue(`<!-- slot/0 -->`);
props.x.use((x) => {
  controller.enqueue(`<template id="0"><div>${x}</div></template>`);
  controller.enqueue(`<script>reframe.replace("slot/0", "template/0")</script>`);
  for (child of children) {
    child.render(controller);
  }
});

serialize(10) -> "10"
serialize({ x: 10 }) -> JSON.stringify({ x: serialize(10) })
serialize(x = async({z: 20, y: async(10)})) -> {
  const id = 12;

  x.use((x) => {
    controller.enqueue(`
      <script>
        reframe.asyncStore.set(${id}, ${serialize(x)});
      </script>
    `);
  });

  return `reframe.asyncStore.create(${id})`;
}

render("state", { value, use }, children)
const id = "12";
controller.enqueue(`<!-- state/12 -->`);
controller.enqueue(`
  <script>
    const element = document.currentScript.previousElementSibling;
    element.value = ${serialize(value)};
    reframe.store.set("state/12", element.value);
  </script>
`);

// <!-- state/12 -->
// <!-- state/12 -->.value : State<number>

render("div", { x: state["12"] }, children): Stream<DOM>
controller.enqueue(`<div x="1">
  <script>
    const s12 = reframe.store.get("state/12"): State<number>;
    s12.use((x) => {
      currentScript.parentElement.setAttribute("x", x);
    });
  </script>
</div>`);

<button
  onClick={<Event<MouseEvent>

  />}
/>
```

```tsx
// @/ai.tsx

'use secret';

const AI = ({prompt, secret, use}) => <OpenAI
  prompt={prompt}
  secret={secret}
  use={use}
/>

// @/message.tsx
<db.Read
  from="app"
  where={{ id: 1 }}
  use:before={(app, next) => (
    <AI
      prompt={app.prompt}
      secret={app.secret}
      use={(message) =>
        next({
          app,
          message,
        })}
    />
  )}
  use={(app) => (
    <json
      value={{
        app,
        message,
      }}
    />
  )}
/>;
```

```tsx
<Async
  value={x}
  use={(x) => (
    <State
      value={x}
      use={(x) => (
        <div>
          {x}
        </div>
      )}
    />
  )}
/>;
```

---

<y>
  <text>todo</text>
  <!-- state/todo -->
    <!-- async/todos -->
      <!-- window -->
      <!-- /window -->

      <!-- state/todos -->
        <y>
          <!-- for -->

          <!-- /for -->
        </y>
      <!-- /state/todos -->
    <!-- async -->

<!-- /state -->
</y>
```

```tsx
<Button
  onClick={
    <Event
      use={(event) => (
        <>
          <SetState
            name="todos"
            set={(todos) => todos.remove(env.todo.id)}
          />
          <db.Delete from="todos" where={{ id: env.todo.id }} />
        </>
      )}
    />
  }
>
  delete
</Button>;

controller.enqueue(
  `<button id="ab" onclick="reframe.emit('event')">delete</button>`,
  `<script>
    const button = document.getElementById('ab');
    button.addEventListener('click', ${render(<Event />)});
  </script>`,
);

`(event) => {
  reframe.store.get("todos", todos => todos.remove(env.todo.id));
  reframe.action.call(
    '$$/db.ts#Delete',
    { from: 'todos', where: { id: env.todo.id } },
  );
}`;
```
