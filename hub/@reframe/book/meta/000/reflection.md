## `task serve`

make it as easy as only one file to get started, like,

```tsx main.tsx
export const App = createApp(...)
```

```bash
deno task serve @<org>/<app>
```

## simplify setup

support directly reading an sqlite file instead of having to set up a server
with turso, the experience should be

```bash
# clone repo
git clone ...

# run an existing app
deno task app:serve @<org>/<app>

# create a new app
deno task app:create @<org>/<app>
```

## set up a new project from the web app

have the ability for user to create new org, and app under their org.

![create app](<Screenshot 2024-10-26 at 6.52.24 AM.png>)

If use creates a new app (they can select a template), it should automatically
create the initial file from that template.

Then if user clicks on that app, it will show the codebase for that app.

Inside, user can list all deployments, navigate all commits and branches, and
create new deployment from any branches.

Initially, commits and branches can be manual similar to git.

Eventually, we want to record each operational transform.
