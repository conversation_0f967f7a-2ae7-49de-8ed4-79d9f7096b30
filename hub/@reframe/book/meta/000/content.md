## Oct 25, 2024

### DJ

We are going to write a book.

The book is about how softwares should be built.

In the first chapter, we are going to write about building UI.

Another option is, we can start building something, and then, we can write about
what we are doing and rearrange that later.

### <PERSON>faque

Yeah that sounds like a good option.

### DJ

Okay, so let's start building reframe.so webapp. It looks like following,

![reframe web](<Screenshot 2024-10-25 at 5.50.05 PM.png>)

This has a community on the right side, and content, like this markdown, on the
left.

### Ashfaque

Is this going to have a different channel / thread for different page / post? Or
only one?

### DJ

Somewhere in the middle, although maybe we can start with only one to start
with. Later, we can show channel based on the topic of the specific page.

What do you think might be the fist step to build this? We can either start with
a database schema or simply the UI with hardcoded content.

### Ashfaque

Starting with hardcoded UI might be easier, also it will give something to look
into.

### DJ

Right, let's start with hardcoded UI then.

To build this in re:frame, we start with creating an app.

One friction I have now is, if we create an app now and then continue to build
that throughout the book, in the end, we only have the final app, not the
intermediate state, because re:frame doesn't yet inherently support versions.

### <PERSON><PERSON>que

Can't we just create each chapter as a commit in git?

### DJ

We can, and probably would for now, however, ideally, we don't want re:frame to
be inside git - the version control system, instead, we want a version control
mechanism inside re:frame that can track changes and let you go back and forward
in time, branch out or merge back.

The reason for that is, git doesn't enforce any order or contraint on what can
be _committed_, and treat a commit as a set of files which were changed. In our
case though, instead of files, we will change and commit specific _structures_,
like a UI component or database schema.

But yeah, let's start with commiting each chapter.

So, to start, we will create an app inside `@reframe/app`. Actually we already
have some old stuff in there, let me clean those up.

Do you know how to set up a new app?

### Ashfaque

No, not really. I guess we manually create app files in a folder?

### DJ

Right

### Ashfaque

Will it be the case that there will be some command like `create-react-app` that
will create a new app?

### DJ

Not sure, because the ideal state is where you'd go to reframe.so, and would
just click a button to create a new app from your dashboard in the browser,
without having to set up anything in your machine.

For now, we will create a new app manually.

First, create a folder `hub/@<org>/<app>`, in this case, use are creating an app
named `app` under org `reframe`.

Then, we'll create an `app.tsx` file inside `hub/@reframe/app`,

```tsx hub/@reframe/app/app.tsx
import { createRoute } from "@reframe/react/router.tsx";

const App = createRoute(
  (Router) => (
    <Router.Route
      page={() => (
        <>
          hello world
        </>
      )}
    />
  ),
);

export default App;
```

`App` will contain the definition of the entire app we are going to build from
now on. Afterwards, create a new file `main.ts` that will create a
[server](./todo) that serves our App.

```tsx hub/@reframe/app/main.ts
import Runtime from "@";
import App from "./app.tsx";

Runtime.serve(App.createServer());
```

Once we have these two files in place, we can run

```bash
deno task watch @reframe/app/main.ts
```

and it should start a server on `http://localhost:8000` - if you go to this
link, you should see something like this,

![hello world](<Screenshot 2024-10-25 at 9.00.42 PM.png>)

You can change the text _hello world_ into anything you link in `app.tsx`, save
and reload the browser, and you should be able to see the changes you made.

Any question so far?

### Ashfaque

No, looks good so far.

### DJ

Cool. I do want to touch on the topic of version control in here again. What we
are writing now in this markdown file, in near future, will be interactive. At
that time, in addition to being able to include images, we will be able to embed
other reframe apps or components in here.

You can imagine this being an interactive guide where we build the app
iteratively that we are building now, and in each chapter, we should be able to
view the app as it was before that chapter.

If we have version control outside reframe, this will require us to make copies
after each iteration into separate apps to be able to access them all in each
chapter.

With integrated versioning, we should be able to run any app at any point in
time, eg: `@reframe/app@xyzabc`, where `xyzabc` would mark a snapshot of the
app.

We will come back to this in later chapters when we get to versions dna
branches.

Tomorrow, we will replace the _hello world_ text with hardcoded UI as we
described above.
