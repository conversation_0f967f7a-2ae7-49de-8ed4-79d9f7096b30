## if you just cloned the repo

```bash
# 1. start a db named `zero.db` on port 8080
turso dev --db-file zero.db

# 2. initialize zero.db (running on port 8080) with the the schema from @reframe/zero
deno task db:migrate @reframe/zero --url http://127.0.0.1:8080
```

# creating a new app

```bash
# create a new app `@<your-org>/<app-name>` from the starter template
deno task create:app @<your-org>/<app-name>


# create a new app `@<your-org>/<app-name>` from @template/<another-template>
deno task create:app @<your-org>/<app-name> --template <another-template>

# create a new app `@<your-org>/<app-name>` from another app `@<another-org>/<another-app>`
deno task create:app @<your-org>/<app-name> --from @<another-org>/<another-app>
```

# running a new app for the first time

```bash
# 1. start a db for your app on port 8100 (make sure to use a different port for each app)
turso dev --db-file <app-name>.db --port 8100

# 2. initialize <app-name>.db (running on port 8100) with the schema from @<your-org>/<app-name>
deno task db:migrate @<your-org>/<app-name> --url http://127.0.0.1:8100

# 3. start the server for your app
deno task watch @<your-org>/<app-name>/main.tsx
```
