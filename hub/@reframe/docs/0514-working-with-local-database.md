1. create a new local database

We use sqlite, and every database is simply a file. To create a new database,
simply select a name, eg: your-db-name, and run the following command

```bash
turso dev --db-file your-db-name.db --port 8100
```

This will create the database your-db-name.db if it doesn't exist, or create a
new one if the file your-db-name.db doesn't exist.

Also make sure the port (8100 in this example), is not used by another database.
If it's not available, simply use any other port (eg: 81xx).

2. making changes to the schema

You can see examples in
[the db/migrations directory of the starter template](../../@template/starter/db/migrations/).

3. running migration and generating types

You need put the `DATABASE_URL` in the `@<org>/<name>/.env` file. Then, run the
following command:

```bash
# deno run --allow-all hub/@reframe/db/migrate.ts @<org>/<name> 
deno run --allow-all hub/@reframe/db/migrate.ts @template/starter
```

Running the command above will run the migration for the app `@template/starter`
(from `@template/starter/db/migrations` directory), and generate types in the
file `@template/starter/db/types.ts` and `@template/starter/db/client.ts`

4. using the database

You can now use the database in your server. Here's an example:

```ts
import Reframe from "@";
import { createClient } from "./db/client.ts";

const client = createClient();
```

when running `deno task watch`
