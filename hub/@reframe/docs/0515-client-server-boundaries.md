# Rendering interactive components on the client

When you build a web app, you need to decide which components should be rendered
on the server, and which should be rendered on the client.

All components start rendering on the server.

```tsx
// /path/to/app.tsx
import { Y, Text } from "@reframe/ui/main.tsx";
import Counter from "./counter.tsx";

export const App = () => {
  return (
    <Y>
      <Text>Hello World</Text>
      <Counter />
    </Y>
  );
};

// /path/to/counter.tsx
import { X, Text, Button } from "@reframe/ui/main.tsx";
import { useState } from "npm:react@canary";

export const Counter = () => {
  const [count, setCount] = useState(0);

  return (
    <X>
      <Text>{count}</Text>
      <Button onClick={() => setCount(count + 1)}>Increment</button>
    </X>
  );
};
```

The code above will render the html, but on the client side, it will break -
because the server will simply render the html, and send it to the client,
without event listeners or state management - which is required for the
`Counter` component to work.

A rule of thumb is, if a component uses any hooks (eg: `useState`) or event
listeners (eg: `onClick`), it's an interactive component, and needs to be
rendered on the client, as well.

To make it interactive, we need to tell our compiler that `Counter` should
_cross the client-server boundary_ and be rendered on the client.

We do that with `with { loader: 'react-client' }` syntax.

```tsx
// /path/to/app.tsx
import { Text, Y } from "@reframe/ui/main.tsx";
import Counter from "./counter.tsx" with { use: "client" };

export const App = () => {
  return (
    <Y>
      <Text>Hello World</Text>
      <Counter />
    </Y>
  );
};
```

Now, when `/path/to/app.tsx` imports `Counter`, it ships the code of `Counter`
to the browser, and tells React how to find the `Counter` component on the
client side.

With this, the `Counter` component will work as expected on the client side.

# Passing data from server to client

When you render a component on the server, you have access to the server's
environment, so you can, for example, fetch data from the database.

```tsx
// /path/to/app.tsx
import { Text, Y } from "@reframe/ui/main.tsx";
import Counter from "./counter.tsx" with { use: "client" };
import { getClient } from "./db/client.ts";

export const App = async () => {
  const db = getClient(process.env.DB_URL);
  const { count } = await db
    .selectFrom("counter")
    .selectAll()
    .where({ id: 1 })
    .executeTakeFirst();

  return (
    <Y>
      <Text>Hello World</Text>
      <Counter initial={count} />
    </Y>
  );
};

// /path/to/counter.tsx
import { X, Text, Button } from "@reframe/ui/main.tsx";
import { useState } from "npm:react@canary";

export const Counter = ({ initial }) => {
  const [count, setCount] = useState(initial);

  return (
    <X>
      <Text>{count}</Text>
      <Button onClick={() => setCount(count + 1)}>Increment</button>
    </X>
  );
};
```

In the code above, the `App` component fetches the initial count from the
database, and passes it to the `Counter` component as a prop, which is then used
to set the initial state of the `Counter` component.

There are two important things to note (and remember) here:

1. Note that the `App` component is an `async` function, we will call them
   _async components_. Remember that, only server components can be async,
   client components cannot be async.
2. A server component can only pass **data** to a client component, it cannot
   pass **functions**. This is because, when a server component passes data to a
   client component, the data is serialized and sent to the client, and
   functions cannot be serialized (yet).

# Making changes to the server from the client

Okay, now what if we want to update the count in the database when the user
clicks the `Increment` button?

Usually, we would need to create a API endpoint on the server, and then call
that endpoint from the client.

But with server actions, we can do this in a much simpler way.

```tsx
// /path/to/counter.tsx

import { X, Text, Button } from "@reframe/ui/main.tsx";
import { useState } from "npm:react@canary";
import { increment } from "./increment.ts";

export const Counter = ({ initial }) => {
  const [count, setCount] = useState(initial);

  return (
    <X>
      <Text>{count}</Text>
      <Button onClick={async () => {
        setCount(count + 1);
        await increment();
      }}>Increment</button>
    </X>
  );
};

// /path/to/increment.ts
import { getClient } from "./db/client.ts";

const db = getClient(process.env.DB_URL);
  
export const increment = async () => {
  return db
    .update("counter")
    .set({ count: { $inc: 1 } })
    .where({ id: 1 })
    .execute();
};
```

The code above will not work, because, since the `Counter` is rendered on the
client, the `increment` function is also executed on the client, and the client
does not have access to the database.

To fix this, we need to tell the compiler that the `increment` function should
be executed on the server.

We do that with `with { loader: 'react-server' }` syntax.

```tsx
// /path/to/counter.tsx

import { X, Text, Button } from "@reframe/ui/main.tsx";
import { useState } from "npm:react@canary";
import { increment } from "./increment.ts" with { use: "server" };

export const Counter = ({ initial }) => {
  const [count, setCount] = useState(initial);

  return (
    <X>
      <Text>{count}</Text>
      <Button onClick={async () => {
        setCount(count + 1);
        await increment();
      }}>Increment</button>
    </X>
  );
};
```
