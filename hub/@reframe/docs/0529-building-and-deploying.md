## Build

To build your app, run the following command:

```bash
deno task build @<org>/<app>/</path/to/main.tsx>
```

This will create a `.build/@<org>/<app>` directory with all the necessary files
to run your app.

To run a built app, you can `cd` into the `.build/@<org>/<app>/src` directory
and run the following command:

```bash
cd .build/@<org>/<app>/src
# note that `start` doesn't read from .env here,
# you'll need to provide env variables as part of the
# command, eg: DATABASE_URL=... deno task start
DATABASE_URL=${url} deno task start
```

## Deploy

To deploy your app, run the following command:

```bash
deno task deploy @<org>/<app>
# run this command from root of the repo(reframe folder), instead of src
```

Make sure you have `DENO_ACCESS_TOKEN`, `DENO_ORG_ID` and `DENO_PROJECT_ID` set
in `hub/.env`.

This will read your app environment from `hub/@<org>/<app>/.env.prod`, hence
make sure you have all production environment variables set there.

Run a db migration if you created a new prod db with following command:

```bash
deno task db:migrate @<org>/<app> --prod
```
