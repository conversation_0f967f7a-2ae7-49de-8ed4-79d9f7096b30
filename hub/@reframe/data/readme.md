# v1

```ts
import { t } from "@reframe/litening";

const user = t
  .columns({
    id: t.integer(),
    name: t.text(),
    email: t.text(),
    createdAt: t.date(),
    metadata: t.json(),
  })
  .primary("id")
  .unique("email")
  .index("createdAt");

const org = t
  .columns({
    id: t.integer(),
    slug: t.text(),
    ownerId: t.integer(),
    name: t.text(),
    createdAt: t.integer(),
  })
  .primary("id")
  .unique("slug")
  .index("ownerId");

const membership = t
  .columns({
    orgId: t.integer(),
    userId: t.integer(),
    role: t.text(),
  })
  .primary("orgId", "userId")
  .index("orgId")
  .index("userId")
  .index("role");

const channel = t
  .columns({
    id: t.integer(),
    orgId: t.integer(),
    name: t.text(),
    createdAt: t.integer(),
  })
  .primary("id")
  .unique("orgId", "name")
  .index("orgId");

const message = t
  .columns({
    id: t.integer(),
    channelId: t.integer(),
    threadId: t.integer().nullable(),
    repliedToId: t.integer().nullable(),
    userId: t.integer(),
    text: t.text(),
    reactionsCount: t.record(t.string(), t.integer()),
    createdAt: t.integer(),
  })
  .primary("id")
  .index("channelId")
  .index("threadId")
  .index("repliedToId")
  .index("userId");

const reaction = t
  .columns({
    emoji: t.text(),
    messageId: t.integer(),
    userId: t.integer(),
    createdAt: t.integer(),
  })
  .primary("emoji", "messageId", "userId")
  .index("messageId")
  .index("userId");

const db = t
  .db({
    user,
    org,
    channel,
    message,
    reaction,
  })
  .auth({
    schema: t.object({
      id: t.integer(),
      email: t.text(),
      role: t.text(),
    }),
    encrypt: (data) => string,
    decrypt: (string) => data,
  })
  .relation((t, auth) => ({
    user: {
      // org: { $many: { ownerId: 'id' }}
      orgs: t.many(t.org, { ownerId: "id" }),
      messages: t.many(t.message, { userId: "id" }),
      reactions: t.many(t.reaction, { userId: "id" }),
      memberships: t.many(t.membership, { userId: "id" }),
    },

    org: {
      owner: t.one(t.user, { ownerId: "id" }),
      channels: t.many(t.channel, { orgId: "id" }),
      memberships: t.many(t.membership, { orgId: "id" }),

      // membership: { $one: { orgId: 'id', userId: { $auth: ['user', 'id' ] } }}
      membership: t.one(t.membership, { orgId: "id", userId: auth.user.id }),
    },

    membership: {
      org: t.one(t.org, { orgId: "id" }),
      user: t.one(t.user, { userId: "id" }),
    },

    channel: {
      org: t.one(t.org, { id: "orgId" }),
      messages: t.many(t.message, { channelId: "id" }),
    },

    message: {
      channel: t.one(t.channel, { id: "channelId" }),
      user: t.one(t.user, { id: "userId" }),
      thread: t.one(t.message, { threadId: "id" }),
      repliedTo: t.one(t.message, { repliedToId: "id" }),
      reactions: t.many(t.reaction, { messageId: "id" }),
    },

    reaction: {
      message: t.one(t.message, { messageId: "id" }),
      user: t.one(t.user, { userId: "id" }),
    },
  }))
  .permission((t) => ({
    user: {
      create: (auth, data) => t.eq(auth.role, "admin"),
      read: (auth, row) =>
        t.or(
          // { $eq: [{ "$": "auth.role" }, "admin"] },
          t.eq(auth.role, "admin"),
          t.eq(row.id, auth.user.id),
        ),
      update: (auth, row, update) => ({
        $or: [
          // { $eq: [auth.role, "admin"] },
          {
            $auth: { role: { $eq: "admin" } },
            $row: {
              id: {
                $eq: { $auth: "user.id" },
              },
              email: { $null: true },
              createdAt: { $null: true },
            },
          },
        ],
      }),
      delete: (auth, row) => ({ $eq: [auth.role, "admin"] }),
    },
    org: {
      create: (auth, data) => t.eq(data.ownerId, auth.user.id),
      read: (auth, row) =>
        t.or(
          t.eq(auth.role, "admin"),
          t.eq(row.ownerId, auth.user.id),
          t.isNotNull(row.membership.role),
        ),
      update: (auth, row, update) =>
        t.or(
          t.eq(auth.role, "admin"),
          t.eq(row.membership.role, "editor"),
          t.and(
            t.eq(row.ownerId, auth.user.id),
            t.isNull(update.id),
            t.isNull(update.createdAt),
          ),
        ),
      delete: (auth, row) => ({
        $or: [
          { $eq: [auth.role, "admin"] },
          { $eq: [row.ownerId, auth.user.id] },
        ],
      }),
    },
  }));

const client = db.connect({ url: ":memory:" });

// const c = connection.auth("token...");

const token = client.sign({ role: "admin" });

Reframe.serve(c.serve);

import { t } from "@reframe/litening/client";

const c = t.connect({ url: "http://localhost:3000" });

c.user.create({ name: "Alice", email: ".." });
```

```tsx
type A1 = Transform<
  Object<{
    a: Object<{
      x: Date;
      y: Date;
    }>;
    b: Object<{
      a: Set<Number>;
      c: Map<String, Number>;
    }>;
  }>,
  Object<{
    a: Object<{
      x: String;
      y: String;
    }>;
    b: Object<{
      a: Array<Number>;
      c: Record<String, number>;
    }>;
  }>
>;

// Shape <Shape> ~= Shape
// Shape <Transform> != Transform

type A2 = Object<{
  a: Object<{
    x: Transform<Date, String>;
    y: Transform<Date, String>;
  }>;
  b: Object<{
    a: Transform<
      Set<Number>,
      Array<Number>
    >;
    c: Transform<
      Map<String, Number>,
      Record<String, Number>
    >;
  }>;
}>;

const shapeA: A2 = {};

console.log(shapeA.read(a1)); // a
console.log(shapeA.write(a)); // a1

/**
 * we want shapes to implement a read method
 */

const a = {
  a: {
    x: "2017-01-01T00:00:00Z",
    y: "2017-01-01T00:00:00Z",
  },
  b: {
    a: [1, 2],
    c: { x: 1, y: 2 },
  },
};

const a1 = {
  a: {
    x: new Date("2017-01-01T00:00:00Z"),
    y: new Date("2017-01-01T00:00:00Z"),
  },
  b: {
    a: new Set([1, 2]),
    c: new Map([
      ["x", 1],
      ["y", 2],
    ]),
  },
};
```

```tsx
const user = t
  .object({
    id: t.integer(),
    good: t.binary(),
    metadata: t.object({
      createdAt: t.date(),
      deleted: t.binary(),
    }),
  });

type ReadUser = (_: {
  id: number;
  good: 0 | 1;
  metadata: {
    createdAt: string;
    deleted: 0 | 1;
  };
}) => {
  id: number;
  good: boolean;
  metadata: {
    createdAt: Date;
    deleted: boolean;
  };
};

user.read({
  id: 1,
  good: 0,
  metadata: {
    createdAt: "2017-01-01T00:00:00Z",
    deleted: 1,
  },
});

user.validate(t);

const t = {
  id: "1",
  good: [0, 1, 0],
  metadata: {
    createdAt: "2017-01-01T00:00:00Z",
    deleted: 1,
  },
};

const t = {
  good: [[[[[[[[[2]]]]]]]]]
  id: [[[[[[[[1]]]]]]]]
}
```

```tsx
((auth, data) => t.eq(auth.user.role, "admin"));
/*
  WHERE
    :auth$user$role = "admin"
*/

const x = {
  "*": t.or(
    t.eq(auth.user.role, "admin"),
    t.and(
      t.eq(row.id, auth.user.id),
    ),
  ),
  email: false,
};
```

```ts
from(
  from(db.org)
    .leftJoin(
      "owner",
      from(db.user)
        .select((owner) => owner)
        .where((owner) => t.eq(owner.id, auth.user.id))
        .limit(1),
      (org, owner) => t.eq(org.ownerId, owner.id),
    )
    .leftJoin(
      from(db.membership)
        .leftJoin(
          "org",
          from(db.org)
            .leftJoin(
              "owner",
              from(db.user)
                .select((owner) => owner)
                .where((owner) => t.eq(owner.id, auth.user.id))
                .limit(1),
              (org, owner) => t.eq(org.ownerId, owner.id),
            )
            .leftJoin(
              "membership",
              from(db.membership)
                .select((membership) => membership)
                .where(
                  (membership) => t.eq(membership.userId, auth.user.id),
                )
                .limit(1),
              (membership, org) =>
                t.and(
                  t.eq(membership.orgId, org.id),
                  t.eq(membership.userId, auth.user.id),
                ),
            )
            .select((org, { owner, membership }) => ({
              ...org,
              owner,
              membership,
            }))
            .where((org) => t.eq(org.id, membership.orgId))
            .limit(1),
          (membership, org) => t.eq(membership.orgId, org.id),
        )
        .select((membership) => ({
          ...membership,
        })),
    )
    .select((org) => ({
      ...org,
      owner,
    })),
).select(({
  id,
  slug,
  ownerId,
}) => ({
  row: t.jsonObject({
    id,
    slug,
    ownerId,
    owner: t.if(
      t.isNull(owner),
      null,
      t.jsonObject({
        id: owner.id,
        email: owner.email,
      }),
    ),
    membership: t.if(
      t.isNull(membership),
      null,
      t.jsonObject({
        orgId: membership.orgId,
        userId: membership.userId,
        role: membership.role,
      }),
    ),
  }),
}));
```

- [1.0] relation, permission, authed crud
  - [x] the types mostly works
  - [x] shapes support read()
  - [x] relations are passed to permission
  - single level create
    - with permission
  - single level read
    - without query
    - with single-level query
    - with multi-level query
  - single level update
    - without query
    - with query
  - single level delete
    - without query
    - with query
  - multi level read
  - multi level create
  - multi level update
  - _not in scope_
    - nested create, update, delete
    - KeyOf is implemented
- [2.0] pull schema, push schema
- [3.0] server / client
- [4.0] client ui (for accessing data)
- [5.0] server ui (for accessing schema)
- [6.0] actions in client ui
  - insert into actions <path#fn, token | null>
  - only for client admins, runs on client env
  - can be triggered by end user with their token (if token = null), or attached
    token
  - theoritically end users can also run actions, we can spawn new deno isolate
    on @user folder with no access to env or parent folders
- [] computed / derived columns

- launch plan
  - the orm works - prisma + auth, github, jsr, turso
  - the communiblog (slack+canvas) is designed as a single page app in
    excalidraw
  - schema is ready for the communiblog
  - communiblog is designed with turbopuffer fidelity
  - markdown renderred component is ready with plain text markdown editor
  - write about the communiblog and orm
